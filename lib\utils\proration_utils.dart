/// 바라 부스 매니저 - 프로레이션 계산 유틸리티
///
/// 구독 플랜 변경시 남은 일수를 계산하여 차액을 결제하는 기능을 제공합니다.
/// - 플러스에서 프로 플랜 전환시 차액 계산
/// - 남은 일수 기반 프로레이션 계산
/// - 결제 금액 계산
///
/// 작성자: Blue
/// 버전: 1.0.0
/// 최종 업데이트: 2025년 8월
library;

import '../models/subscription_plan.dart';
import '../utils/logger_utils.dart';

/// 프로레이션 계산 결과
class ProrationResult {
  /// 현재 플랜 남은 일수
  final int remainingDays;
  
  /// 현재 플랜 남은 금액 (환불될 금액)
  final int currentPlanRefund;
  
  /// 새 플랜 전체 기간 금액
  final int newPlanFullAmount;
  
  /// 새 플랜 남은 기간 금액
  final int newPlanProrationAmount;
  
  /// 실제 결제할 차액 (새 플랜 금액 - 현재 플랜 환불 금액)
  final int chargeAmount;
  
  /// 새 플랜 다음 결제일
  final DateTime nextPaymentDate;

  const ProrationResult({
    required this.remainingDays,
    required this.currentPlanRefund,
    required this.newPlanFullAmount,
    required this.newPlanProrationAmount,
    required this.chargeAmount,
    required this.nextPaymentDate,
  });

  /// 프로레이션이 필요한지 확인 (차액이 있는지)
  bool get hasChargeAmount => chargeAmount > 0;
  
  /// 환불이 있는지 확인
  bool get hasRefund => currentPlanRefund > 0;
  
  @override
  String toString() {
    return 'ProrationResult(remainingDays: $remainingDays, '
           'currentPlanRefund: $currentPlanRefund, '
           'newPlanFullAmount: $newPlanFullAmount, '
           'newPlanProrationAmount: $newPlanProrationAmount, '
           'chargeAmount: $chargeAmount, '
           'nextPaymentDate: $nextPaymentDate)';
  }
}

/// 프로레이션 계산 유틸리티
class ProrationUtils {
  static const String _tag = 'ProrationUtils';
  
  /// 플랜 변경시 프로레이션 계산
  /// 
  /// [currentPlan] 현재 구독 플랜
  /// [newPlan] 변경할 새 플랜
  /// [currentSubscriptionEndDate] 현재 구독 만료일
  /// [changeDate] 플랜 변경일 (기본값: 현재 시간)
  static ProrationResult calculateProration({
    required SubscriptionPlan currentPlan,
    required SubscriptionPlan newPlan,
    required DateTime currentSubscriptionEndDate,
    DateTime? changeDate,
  }) {
    final now = changeDate ?? DateTime.now();
    
    LoggerUtils.logInfo(
      '프로레이션 계산 시작: ${currentPlan.name} -> ${newPlan.name}',
      tag: _tag,
    );
    
    // 현재 구독의 남은 일수 계산 (+1일 보정 - 당일 포함)
    final remainingDays = currentSubscriptionEndDate.difference(now).inDays + 1;
    
    if (remainingDays <= 0) {
      LoggerUtils.logWarning('현재 구독이 이미 만료됨', tag: _tag);
      return ProrationResult(
        remainingDays: 0,
        currentPlanRefund: 0,
        newPlanFullAmount: newPlan.monthlyPrice,
        newPlanProrationAmount: newPlan.monthlyPrice,
        chargeAmount: newPlan.monthlyPrice,
        nextPaymentDate: now.add(const Duration(days: 30)),
      );
    }
    
    // 현재 플랜의 일할 환불 금액 계산 (30일 기준)
    final currentPlanDailyRate = currentPlan.monthlyPrice / 30;
    final currentPlanRefund = (currentPlanDailyRate * remainingDays).round();
    
    // 새 플랜의 일할 금액 계산 (남은 기간에 대해)
    final newPlanDailyRate = newPlan.monthlyPrice / 30;
    final newPlanProrationAmount = (newPlanDailyRate * remainingDays).round();
    
    // 실제 결제할 차액 계산
    final chargeAmount = newPlanProrationAmount - currentPlanRefund;
    
    // 다음 결제일은 현재 구독 만료일과 동일
    final nextPaymentDate = currentSubscriptionEndDate;
    
    final result = ProrationResult(
      remainingDays: remainingDays,
      currentPlanRefund: currentPlanRefund,
      newPlanFullAmount: newPlan.monthlyPrice,
      newPlanProrationAmount: newPlanProrationAmount,
      chargeAmount: chargeAmount > 0 ? chargeAmount : 0, // 음수면 0으로 처리
      nextPaymentDate: nextPaymentDate,
    );
    
    LoggerUtils.logInfo('프로레이션 계산 완료: $result', tag: _tag);
    
    return result;
  }
  
  /// 프로레이션 설명 텍스트 생성
  static String generateProrationDescription(ProrationResult proration, 
      SubscriptionPlan currentPlan, SubscriptionPlan newPlan) {
    final buffer = StringBuffer();
    
    buffer.writeln('플랜 변경 상세 내역:');
    buffer.writeln('');
    buffer.writeln('• 현재 플랜: ${currentPlan.name}');
    buffer.writeln('• 새 플랜: ${newPlan.name}');
    buffer.writeln('• 남은 기간: ${proration.remainingDays}일');
    buffer.writeln('');
    
    if (proration.hasRefund) {
      buffer.writeln('• 현재 플랜 환불: ${formatCurrency(proration.currentPlanRefund)}');
    }

    buffer.writeln('• 새 플랜 비용 (${proration.remainingDays}일): ${formatCurrency(proration.newPlanProrationAmount)}');
    buffer.writeln('');

    if (proration.hasChargeAmount) {
      buffer.writeln('• 추가 결제 금액: ${formatCurrency(proration.chargeAmount)}');
    } else {
      buffer.writeln('• 추가 결제 없음');
    }

    buffer.writeln('• 다음 정기 결제일: ${_formatDate(proration.nextPaymentDate)}');
    buffer.writeln('• 다음 정기 결제 금액: ${formatCurrency(newPlan.monthlyPrice)}');
    
    return buffer.toString();
  }
  
  /// 통화 포맷팅
  static String formatCurrency(int amount) {
    return '${amount.toString().replaceAllMapped(
      RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
      (Match m) => '${m[1]},',
    )}원';
  }
  
  /// 날짜 포맷팅
  static String _formatDate(DateTime date) {
    return '${date.year}년 ${date.month}월 ${date.day}일';
  }
  
  /// 플랜 업그레이드 가능 여부 확인
  static bool canUpgrade(SubscriptionPlanType from, SubscriptionPlanType to) {
    // 무료 -> 플러스/프로, 플러스 -> 프로만 허용
    switch (from) {
      case SubscriptionPlanType.free:
        return to == SubscriptionPlanType.plus || to == SubscriptionPlanType.pro;
      case SubscriptionPlanType.plus:
        return to == SubscriptionPlanType.pro;
      case SubscriptionPlanType.pro:
        return false; // 프로에서는 업그레이드 불가
    }
  }
  
  /// 플랜 다운그레이드 가능 여부 확인
  static bool canDowngrade(SubscriptionPlanType from, SubscriptionPlanType to) {
    // 프로 -> 플러스/무료, 플러스 -> 무료만 허용
    switch (from) {
      case SubscriptionPlanType.pro:
        return to == SubscriptionPlanType.plus || to == SubscriptionPlanType.free;
      case SubscriptionPlanType.plus:
        return to == SubscriptionPlanType.free;
      case SubscriptionPlanType.free:
        return false; // 무료에서는 다운그레이드 불가
    }
  }
}
