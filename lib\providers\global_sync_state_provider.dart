import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../utils/logger_utils.dart';

/// 전역 동기화 상태
enum GlobalSyncStatus {
  idle,           // 대기 상태
  syncing,        // 동기화 중
  success,        // 동기화 성공
  error,          // 동기화 실패
  paused,         // 일시 중지
}

/// 동기화 타입
enum SyncType {
  initial,        // 초기 동기화
  realtime,       // 실시간 동기화
  manual,         // 수동 동기화
  background,     // 백그라운드 동기화
}

/// 전역 동기화 상태 데이터
class GlobalSyncState {
  final GlobalSyncStatus status;
  final SyncType? currentSyncType;
  final String? message;
  final double progress;
  final String? errorMessage;
  final DateTime? lastSyncTime;
  final bool isSyncInProgress;
  final Set<String> activeSyncOperations;

  const GlobalSyncState({
    this.status = GlobalSyncStatus.idle,
    this.currentSyncType,
    this.message,
    this.progress = 0.0,
    this.errorMessage,
    this.lastSyncTime,
    this.isSyncInProgress = false,
    this.activeSyncOperations = const {},
  });

  GlobalSyncState copyWith({
    GlobalSyncStatus? status,
    SyncType? currentSyncType,
    String? message,
    double? progress,
    String? errorMessage,
    DateTime? lastSyncTime,
    bool? isSyncInProgress,
    Set<String>? activeSyncOperations,
  }) {
    return GlobalSyncState(
      status: status ?? this.status,
      currentSyncType: currentSyncType ?? this.currentSyncType,
      message: message ?? this.message,
      progress: progress ?? this.progress,
      errorMessage: errorMessage ?? this.errorMessage,
      lastSyncTime: lastSyncTime ?? this.lastSyncTime,
      isSyncInProgress: isSyncInProgress ?? this.isSyncInProgress,
      activeSyncOperations: activeSyncOperations ?? this.activeSyncOperations,
    );
  }
}

/// 전역 동기화 상태 관리자
class GlobalSyncStateNotifier extends StateNotifier<GlobalSyncState> {
  static const String _tag = 'GlobalSyncStateNotifier';
  
  // 동시 동기화 방지를 위한 락
  final Set<String> _syncLocks = {};
  
  GlobalSyncStateNotifier() : super(const GlobalSyncState()) {
    _loadLastSyncTime();
  }

  /// 마지막 동기화 시간 로드
  Future<void> _loadLastSyncTime() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final lastSyncTimeMs = prefs.getInt('last_sync_time');
      if (lastSyncTimeMs != null) {
        final lastSyncTime = DateTime.fromMillisecondsSinceEpoch(lastSyncTimeMs);
        state = state.copyWith(lastSyncTime: lastSyncTime);
      }
    } catch (e) {
      LoggerUtils.logError('마지막 동기화 시간 로드 실패', tag: _tag, error: e);
    }
  }

  /// 마지막 동기화 시간 저장
  Future<void> _saveLastSyncTime(DateTime time) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt('last_sync_time', time.millisecondsSinceEpoch);
    } catch (e) {
      LoggerUtils.logError('마지막 동기화 시간 저장 실패', tag: _tag, error: e);
    }
  }

  /// 동기화 시작 (동시 동기화 방지)
  bool startSync(String operationId, SyncType syncType) {
    // 이미 같은 작업이 진행 중인지 확인
    if (_syncLocks.contains(operationId)) {
      LoggerUtils.logWarning('동기화 작업이 이미 진행 중: $operationId', tag: _tag);
      return false;
    }

    // 다른 동기화 작업이 진행 중인지 확인
    if (state.isSyncInProgress && syncType != SyncType.realtime) {
      LoggerUtils.logWarning('다른 동기화 작업이 진행 중 - 요청 거부: $operationId', tag: _tag);
      return false;
    }

    // 동기화 시작
    _syncLocks.add(operationId);
    final newActiveSyncOperations = Set<String>.from(state.activeSyncOperations)..add(operationId);
    
    state = state.copyWith(
      status: GlobalSyncStatus.syncing,
      currentSyncType: syncType,
      isSyncInProgress: true,
      activeSyncOperations: newActiveSyncOperations,
      errorMessage: null,
      progress: 0.0,
    );

    LoggerUtils.logInfo('동기화 시작: $operationId (타입: $syncType)', tag: _tag);
    return true;
  }

  /// 동기화 진행률 업데이트
  void updateProgress(String operationId, double progress, String? message) {
    if (!_syncLocks.contains(operationId)) {
      LoggerUtils.logWarning('등록되지 않은 동기화 작업: $operationId', tag: _tag);
      return;
    }

    state = state.copyWith(
      progress: progress,
      message: message,
    );

    LoggerUtils.logDebug('동기화 진행률 업데이트: $operationId - ${(progress * 100).toInt()}%', tag: _tag);
  }

  /// 동기화 완료
  void completeSync(String operationId) {
    if (!_syncLocks.contains(operationId)) {
      LoggerUtils.logWarning('등록되지 않은 동기화 작업 완료 시도: $operationId', tag: _tag);
      return;
    }

    _syncLocks.remove(operationId);
    final newActiveSyncOperations = Set<String>.from(state.activeSyncOperations)..remove(operationId);
    final now = DateTime.now();

    state = state.copyWith(
      status: GlobalSyncStatus.success,
      progress: 1.0,
      message: '동기화 완료',
      lastSyncTime: now,
      isSyncInProgress: newActiveSyncOperations.isNotEmpty,
      activeSyncOperations: newActiveSyncOperations,
      errorMessage: null,
    );

    _saveLastSyncTime(now);
    LoggerUtils.logInfo('동기화 완료: $operationId', tag: _tag);

    // 모든 동기화가 완료되면 상태를 idle로 변경
    if (newActiveSyncOperations.isEmpty) {
      Future.delayed(const Duration(seconds: 2), () {
        if (mounted && state.activeSyncOperations.isEmpty) {
          state = state.copyWith(status: GlobalSyncStatus.idle);
        }
      });
    }
  }

  /// 동기화 실패
  void failSync(String operationId, String errorMessage) {
    if (!_syncLocks.contains(operationId)) {
      LoggerUtils.logWarning('등록되지 않은 동기화 작업 실패 시도: $operationId', tag: _tag);
      return;
    }

    _syncLocks.remove(operationId);
    final newActiveSyncOperations = Set<String>.from(state.activeSyncOperations)..remove(operationId);

    state = state.copyWith(
      status: GlobalSyncStatus.error,
      errorMessage: errorMessage,
      isSyncInProgress: newActiveSyncOperations.isNotEmpty,
      activeSyncOperations: newActiveSyncOperations,
    );

    LoggerUtils.logError('동기화 실패: $operationId - $errorMessage', tag: _tag);
  }

  /// 동기화 일시 중지
  void pauseSync(String operationId) {
    if (!_syncLocks.contains(operationId)) {
      LoggerUtils.logWarning('등록되지 않은 동기화 작업 일시 중지 시도: $operationId', tag: _tag);
      return;
    }

    state = state.copyWith(
      status: GlobalSyncStatus.paused,
      message: '동기화 일시 중지',
    );

    LoggerUtils.logInfo('동기화 일시 중지: $operationId', tag: _tag);
  }

  /// 에러 상태 클리어
  void clearError() {
    state = state.copyWith(
      status: state.isSyncInProgress ? GlobalSyncStatus.syncing : GlobalSyncStatus.idle,
      errorMessage: null,
    );
  }

  /// 모든 동기화 강제 중단
  void forceStopAllSync() {
    LoggerUtils.logWarning('모든 동기화 강제 중단', tag: _tag);
    
    _syncLocks.clear();
    state = state.copyWith(
      status: GlobalSyncStatus.idle,
      isSyncInProgress: false,
      activeSyncOperations: const {},
      message: '동기화 중단됨',
    );
  }

  /// 동기화 상태 정보
  Map<String, dynamic> getSyncInfo() {
    return {
      'status': state.status.toString(),
      'currentSyncType': state.currentSyncType?.toString(),
      'progress': state.progress,
      'isSyncInProgress': state.isSyncInProgress,
      'activeSyncOperations': state.activeSyncOperations.toList(),
      'syncLocks': _syncLocks.toList(),
      'lastSyncTime': state.lastSyncTime?.toIso8601String(),
    };
  }
}

/// 전역 동기화 상태 Provider
final globalSyncStateProvider = StateNotifierProvider<GlobalSyncStateNotifier, GlobalSyncState>((ref) {
  return GlobalSyncStateNotifier();
});

/// 동기화 진행 중 여부 Provider
final isSyncInProgressProvider = Provider<bool>((ref) {
  return ref.watch(globalSyncStateProvider).isSyncInProgress;
});

/// 마지막 동기화 시간 Provider
final lastSyncTimeProvider = Provider<DateTime?>((ref) {
  return ref.watch(globalSyncStateProvider).lastSyncTime;
});
