/// 바라 부스 매니저 - 사용자 역할 열거형
///
/// 행사에서의 사용자 역할을 나타내는 열거형입니다.
/// - owner: 행사 소유자 (모든 권한)
/// - invited: 초대받은 사용자 (제한된 권한)
///
/// 작성자: Blue
/// 버전: 1.0.0
/// 최종 업데이트: 2025년 8월
library;

/// 사용자 역할을 나타내는 열거형
enum UserRole {
  /// 행사 소유자 (모든 권한)
  owner('owner', '소유자'),
  
  /// 초대받은 사용자 (제한된 권한)
  invited('invited', '초대됨');

  const UserRole(this.value, this.displayName);

  /// 데이터베이스 저장용 값
  final String value;
  
  /// 사용자에게 표시할 이름
  final String displayName;

  /// 문자열 값으로부터 UserRole 생성
  static UserRole fromString(String value) {
    switch (value) {
      case 'owner':
        return UserRole.owner;
      case 'invited':
        return UserRole.invited;
      default:
        throw ArgumentError('Unknown user role: $value');
    }
  }

  /// JSON 직렬화를 위한 문자열 변환
  String toJson() => value;

  /// JSON 역직렬화를 위한 팩토리 생성자
  static UserRole fromJson(String json) => fromString(json);

  /// 소유자 권한 여부 확인
  bool get isOwner => this == UserRole.owner;

  /// 초대받은 사용자 여부 확인
  bool get isInvited => this == UserRole.invited;
}
