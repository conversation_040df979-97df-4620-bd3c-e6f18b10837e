# 바라 부스 매니저 - 사용자 가이드

## 📋 개요

바라 부스 매니저는 행사 부스 판매 관리를 위한 완전 자동화된 시스템입니다. 이 가이드는 앱의 주요 기능과 사용법을 안내합니다.

## 🚀 시작하기

### 앱 실행
1. 앱 아이콘을 탭하여 바라 부스 매니저 실행
2. 초기 로딩 화면에서 데이터베이스 초기화 대기
3. 메인 화면이 표시되면 사용 준비 완료

### 주요 화면 구성
- **상품 관리**: 상품 등록, 수정, 삭제
- **판매 관리**: 판매 등록, 이력 조회
- **선입금 관리**: 선입금 등록, 수령, 조회회
- **통계**: 매출 통계, 판매 분석
- **설정**: 앱 설정, 데이터 관리

---

## 🛍️ 상품 관리

### 상품 등록
1. **상품 관리** 탭 선택
2. **+ 새 상품 등록** 버튼 탭
3. 다음 정보 입력:
   - **상품명**: 상품의 이름 (필수)
   - **가격**: 판매 가격 (필수)
   - **수량**: 재고 수량 (필수)
   - **판매자**: 판매자 이름 (필수)
   - **설명**: 상품 설명 (선택)
   - **이미지**: 상품 이미지 (선택)
4. **등록** 버튼 탭

### 상품 수정
1. 상품 목록에서 수정할 상품 선택
2. **편집** 버튼 탭
3. 필요한 정보 수정
4. **저장** 버튼 탭

### 상품 삭제
1. 상품 목록에서 삭제할 상품 선택
2. **삭제** 버튼 탭
3. 확인 대화상자에서 **확인** 탭

### 상품 검색 및 필터링
- **검색**: 상단 검색창에 상품명 입력
- **판매자별 필터**: 판매자 선택 드롭다운 사용
- **정렬**: 가격, 이름, 수량, 최근등록 순으로 정렬

---

## 💰 판매 관리

### 판매 등록
1. **판매** 버튼 선택
3. 다음 정보 입력:
   - **상품 선택**: 판매할 상품 선택
   - **수량**: 판매 수량만큼 누르기
4. **판매 완료** 버튼 탭

### 판매 이력 조회
1. **판매 이력** 탭 선택
2. 판매 기록 목록 확인
3. **필터링 옵션**:
   - 날짜 범위 선택
   - 판매자별 필터
   - 상품별 필터
   - 결제 방법별 필터

### 판매 수정/취소
1. 판매 이력에서 해당 판매 선택
2. **편집** 또는 **취소** 버튼 탭
3. 수정 사항 입력 또는 취소 확인
4. **저장** 또는 **확인** 버튼 탭

---

## 💳 선입금 관리

### 선입금 등록
1. **선입금 관리** 탭 선택
2. **+ 새 선입금 등록** 버튼 탭
3. 다음 정보 입력:
   - **구매자명**: 선입금 구매자 이름 (필수)
   - **금액**: 선입금 금액 (필수)
   - **상품목록**: 선입금으로 구매한 상품 목록 가상 상품 데이터로 생성 (필수)
   - **은행명**: 선입금 구매자 환불 은행 정보 (필수)
   - **이메일**: 선입금 구매자 이메일 (필수)
   - **트위터 계정**: 선입금 구매자 트위터 계정 (선택)
   - **메모**: 메모 (선택)
   - **수령요일**: 선입금 목록 수령할 요일 복수선택가능 (필수수)
4. **등록** 버튼 탭

### 선입금 수령
1. **선입금 목록** 화면에서 **미수령** 버튼 탭
2. 다시 미수령으로 바꾸고싶으면 **수령** 버튼 탭

### 선입금 조회
- **선입금 목록**: 모든 선입금 조회
- **상세보기** 각 선입금 누르면 상세내용 출력

---

## 📊 통계 및 분석

### 매출 통계
1. **통계** 탭 선택
2. **매출 통계** 섹션 확인:
   - **일별 매출**: 날짜별 매출 현황
   - **월별 매출**: 월별 매출 현황
   - **판매자별 매출**: 판매자별 매출 현황
   - **상품별 매출**: 상품별 매출 현황

### 판매 분석
- **인기 상품**: 판매량 기준 상위 상품
- **판매 추이**: 시간대별 판매 패턴
- **재고 현황**: 상품별 재고 상태
- **수익성 분석**: 상품별 수익률

---

## ⚙️ 설정

### 앱 설정
1. **설정** 탭 선택
2. **앱 설정** 섹션에서:
   - **테마**: 다크/라이트 모드 선택
   - **언어**: 한국어/영어 선택
   - **알림**: 푸시 알림 설정
   - **자동 저장**: 자동 저장 간격 설정

### 데이터 관리
- **데이터 백업**: 로컬 데이터 백업
- **데이터 복원**: 백업 데이터 복원
- **데이터 초기화**: 모든 데이터 삭제
- **캐시 정리**: 임시 데이터 정리

### 보안 설정
- **비밀번호 설정**: 앱 접근 비밀번호
- **자동 잠금**: 일정 시간 후 자동 잠금
- **데이터 암호화**: 민감한 데이터 암호화

---

## 🔍 문제 해결

### 일반적인 문제들

#### 앱이 느리게 실행됩니다
1. **캐시 정리**:
   - 설정 → 데이터 관리 → 캐시 정리
2. **불필요한 데이터 삭제**:
   - 오래된 판매 이력 삭제
   - 사용하지 않는 상품 삭제
3. **앱 재시작**:
   - 앱을 완전히 종료 후 재시작

#### 데이터가 저장되지 않습니다
1. **저장 공간 확인**:
   - 디바이스 저장 공간 확인
   - 충분한 여유 공간 확보
2. **권한 확인**:
   - 저장소 접근 권한 확인
   - 권한 재설정
3. **앱 재설치**:
   - 앱 삭제 후 재설치

#### 검색이 작동하지 않습니다
1. **검색어 확인**:
   - 정확한 상품명 입력
   - 대소문자 구분 확인
2. **필터 초기화**:
   - 모든 필터 해제
   - 검색창 초기화
3. **앱 재시작**:
   - 앱을 완전히 종료 후 재시작

#### 선입금 사용이 안됩니다
1. **선입금 상태 확인**:
   - 미사용 상태인지 확인
   - 만료일 확인
2. **잔액 확인**:
   - 사용 가능한 잔액 확인
   - 사용 금액이 잔액 이하인지 확인
3. **선입금 재선택**:
   - 다른 선입금으로 시도

### 오류 메시지 해결

#### "데이터베이스 연결 오류"
1. 앱 재시작
2. 디바이스 재부팅
3. 앱 재설치

#### "메모리 부족 오류"
1. 다른 앱 종료
2. 캐시 정리
3. 불필요한 데이터 삭제

#### "권한 오류"
1. 설정에서 권한 확인
2. 권한 재설정
3. 앱 재설치

---

## 💡 사용 팁

### 효율적인 상품 관리
- **카테고리별 분류**: 상품명에 카테고리 포함
- **정기적 재고 확인**: 수량이 적은 상품 모니터링
- **가격 업데이트**: 시장 상황에 맞춰 가격 조정

### 빠른 판매 등록
- **자주 사용하는 상품**: 즐겨찾기 설정
- **단축키 활용**: 키보드 단축키 사용
- **일괄 등록**: 여러 상품 동시 등록

### 정확한 통계 관리
- **정기적 백업**: 중요한 데이터 정기 백업
- **데이터 검증**: 입력 데이터 정확성 확인
- **기간별 분석**: 월별, 분기별 분석 활용

### 보안 강화
- **정기적 비밀번호 변경**: 보안 강화
- **자동 잠금 설정**: 개인정보 보호
- **데이터 암호화**: 민감한 정보 보호

---

## 📞 지원 및 문의

### 도움말
- **앱 내 도움말**: 각 화면의 **?** 버튼 탭
- **온라인 가이드**: 공식 웹사이트 방문
- **FAQ**: 자주 묻는 질문 확인

### 기술 지원
- **이메일**: <EMAIL>
- **전화**: 1588-0000 (평일 9:00-18:00)
- **채팅**: 앱 내 실시간 채팅

### 피드백
- **앱 평가**: 앱스토어/플레이스토어 평가
- **버그 신고**: 설정 → 피드백 → 버그 신고
- **기능 제안**: 설정 → 피드백 → 기능 제안

---

## 📱 플랫폼별 특성

### Android
- **백 버튼**: 뒤로 가기
- **홈 버튼**: 앱 최소화
- **멀티태스킹**: 최근 앱에서 복귀

### iOS
- **스와이프 제스처**: 뒤로 가기
- **홈 인디케이터**: 앱 전환
- **3D 터치**: 빠른 액션

### Web
- **브라우저 뒤로가기**: 이전 화면
- **새 탭**: 새 창에서 열기
- **북마크**: 자주 사용하는 페이지 저장

---

## 🔄 업데이트

### 자동 업데이트
- **앱스토어/플레이스토어**: 자동 업데이트 설정
- **앱 내 알림**: 업데이트 알림 수신

### 수동 업데이트
1. **설정** → **앱 정보** → **업데이트 확인**
2. 업데이트가 있으면 **업데이트** 버튼 탭
3. 업데이트 완료 후 앱 재시작

### 업데이트 노트
- **새로운 기능**: 추가된 기능 확인
- **개선사항**: 성능 개선 내용
- **버그 수정**: 수정된 문제점

---

**작성자**: Blue  
**버전**: 1.0.0  
**최종 업데이트**: 2025년 1월 
