import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../models/event_workspace.dart';
import '../../providers/unified_workspace_provider.dart';
import '../../providers/category_provider.dart';
import '../../utils/logger_utils.dart';
import '../../utils/app_colors.dart';
import '../../utils/responsive_helper.dart';

/// 온보딩 완료 후 전용 초기화 화면
/// 
/// 복잡한 상태 동기화 대신 모든 시스템을 순차적으로 초기화한 후
/// 완전히 준비된 상태에서 메인 앱으로 진입합니다.
class PostOnboardingInitScreen extends ConsumerStatefulWidget {
  final EventWorkspace createdWorkspace;
  final VoidCallback onInitializationComplete;

  const PostOnboardingInitScreen({
    super.key,
    required this.createdWorkspace,
    required this.onInitializationComplete,
  });

  @override
  ConsumerState<PostOnboardingInitScreen> createState() => _PostOnboardingInitScreenState();
}

class _PostOnboardingInitScreenState extends ConsumerState<PostOnboardingInitScreen>
    with TickerProviderStateMixin {
  static const String _tag = 'PostOnboardingInitScreen';

  // 애니메이션 컨트롤러들
  late AnimationController _pulseController;
  late AnimationController _progressController;
  late Animation<double> _pulseAnimation;
  late Animation<double> _progressAnimation;

  // 초기화 상태
  String _currentStep = '행사를 생성하는 중...';
  bool _hasError = false;
  String _errorMessage = '';

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _startInitialization();
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _progressController.dispose();
    super.dispose();
  }

  /// 애니메이션 초기화
  void _initializeAnimations() {
    _pulseController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );
    
    _progressController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _pulseAnimation = Tween<double>(
      begin: 0.8,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));

    _progressAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _progressController,
      curve: Curves.easeOut,
    ));

    _pulseController.repeat(reverse: true);
  }

  /// 시스템 초기화 시작
  Future<void> _startInitialization() async {
    try {
      LoggerUtils.logInfo('온보딩 후 행사 생성 시작: ${widget.createdWorkspace.name}', tag: _tag);

      // 단순화된 초기화: 통합 워크스페이스 Provider 사용
      await _updateStep('통합 워크스페이스 초기화 중...', 0.3);
      
      // 통합 워크스페이스 Provider로 워크스페이스 전환
      await ref.read(unifiedWorkspaceProvider.notifier).switchToWorkspace(widget.createdWorkspace);
      
      // 기본 카테고리 생성 확인 및 생성
      await _updateStep('기본 카테고리 설정 중...', 0.7);
      await ref.read(categoryNotifierProvider.notifier).createDefaultCategoryForEvent(widget.createdWorkspace.id);
      
      await _updateStep('시스템 초기화 완료!', 1.0);
      await Future.delayed(const Duration(milliseconds: 800));

      LoggerUtils.logInfo('온보딩 후 행사 생성 완료', tag: _tag);

      if (mounted) {
        // 완료 콜백 호출 후 약간의 지연
        await Future.delayed(const Duration(milliseconds: 200));

        // 직접 메인 화면으로 이동
        LoggerUtils.logInfo('메인 화면으로 이동', tag: _tag);
        widget.onInitializationComplete();

        // Navigator를 사용해서 모든 온보딩 화면을 제거하고 메인 화면으로 이동
        if (mounted) {
          Navigator.of(context).pushNamedAndRemoveUntil(
            '/', // 메인 화면 라우트
            (route) => false, // 모든 이전 화면 제거
          );
        }
      }

    } catch (e, stackTrace) {
      LoggerUtils.logError('온보딩 후 시스템 초기화 실패', tag: _tag, error: e, stackTrace: stackTrace);
      
      if (mounted) {
        setState(() {
          _hasError = true;
          _errorMessage = '행사 생성 중 오류가 발생했습니다: ${e.toString()}';
        });
      }
    }
  }

  /// 단계 업데이트
  Future<void> _updateStep(String step, double progress) async {
    if (mounted) {
      setState(() {
        _currentStep = step;
      });
      
      // 프로그레스 애니메이션
      _progressController.animateTo(progress);
    }
    
    LoggerUtils.logDebug('행사 생성 단계: $step (${(progress * 100).toInt()}%)', tag: _tag);
    await Future.delayed(const Duration(milliseconds: 400));
  }

  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    final isTablet = ResponsiveHelper.isTablet(context);
    
    return Scaffold(
      backgroundColor: AppColors.background,
      body: SafeArea(
        child: Center(
          child: Container(
            width: isTablet ? screenSize.width * 0.6 : screenSize.width * 0.9,
            padding: const EdgeInsets.all(32),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // 로고 또는 아이콘
                AnimatedBuilder(
                  animation: _pulseAnimation,
                  builder: (context, child) {
                    return Transform.scale(
                      scale: _pulseAnimation.value,
                      child: Container(
                        width: 120,
                        height: 120,
                        decoration: BoxDecoration(
                          color: AppColors.onboardingPrimary.withValues(alpha: 0.1),
                          shape: BoxShape.circle,
                          border: Border.all(
                            color: AppColors.onboardingPrimary,
                            width: 2,
                          ),
                        ),
                        child: Icon(
                          _hasError ? Icons.error_outline : Icons.event_note,
                          size: 60,
                          color: _hasError ? Colors.red : AppColors.onboardingPrimary,
                        ),
                      ),
                    );
                  },
                ),
                
                const SizedBox(height: 40),
                
                // 제목
                Text(
                  _hasError ? '행사 생성 오류' : '행사를 생성하는 중',
                  style: TextStyle(
                    fontSize: isTablet ? 28 : 24,
                    fontWeight: FontWeight.bold,
                    color: _hasError ? Colors.red : AppColors.onboardingTextPrimary,
                  ),
                  textAlign: TextAlign.center,
                ),
                
                const SizedBox(height: 16),
                
                // 현재 단계 또는 에러 메시지
                Text(
                  _hasError ? _errorMessage : _currentStep,
                  style: TextStyle(
                    fontSize: isTablet ? 18 : 16,
                    color: _hasError ? Colors.red.shade700 : AppColors.onboardingTextSecondary,
                  ),
                  textAlign: TextAlign.center,
                ),
                
                const SizedBox(height: 40),
                
                // 프로그레스 바 (에러가 아닐 때만)
                if (!_hasError) ...[
                  AnimatedBuilder(
                    animation: _progressAnimation,
                    builder: (context, child) {
                      return Column(
                        children: [
                          LinearProgressIndicator(
                            value: _progressAnimation.value,
                            backgroundColor: AppColors.onboardingPrimary.withValues(alpha: 0.2),
                            valueColor: AlwaysStoppedAnimation<Color>(AppColors.onboardingPrimary),
                            minHeight: 8,
                          ),
                          const SizedBox(height: 16),
                          Text(
                            '${(_progressAnimation.value * 100).toInt()}%',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w500,
                              color: AppColors.onboardingTextSecondary,
                            ),
                          ),
                        ],
                      );
                    },
                  ),
                ],
                
                // 에러 시 재시도 버튼
                if (_hasError) ...[
                  const SizedBox(height: 32),
                  ElevatedButton(
                    onPressed: () {
                      setState(() {
                        _hasError = false;
                        _errorMessage = '';
                        _currentStep = '행사를 생성하는 중...';
                      });
                      _startInitialization();
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.onboardingPrimary,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: const Text(
                      '다시 시도',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }
}
