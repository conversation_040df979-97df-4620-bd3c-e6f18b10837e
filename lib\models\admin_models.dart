// 관리자 페이지용 모델 클래스들

/// 관리자 대시보드 통계
class AdminDashboardStats {
  final int totalUsers;
  final int activeSubscribers;
  final int freeUsers;
  final int recentUsers;
  final int weeklyUsers;
  final double totalRevenue;
  final List<MonthlyRevenue> monthlyRevenue;
  final ServerStats serverStats;
  final UserActivityStats userActivityStats;
  final GrowthRate growthRate;

  AdminDashboardStats({
    required this.totalUsers,
    required this.activeSubscribers,
    required this.freeUsers,
    required this.recentUsers,
    required this.weeklyUsers,
    required this.totalRevenue,
    required this.monthlyRevenue,
    required this.serverStats,
    required this.userActivityStats,
    required this.growthRate,
  });

  factory AdminDashboardStats.fromJson(Map<String, dynamic> json) {
    return AdminDashboardStats(
      totalUsers: json['totalUsers'] ?? 0,
      activeSubscribers: json['activeSubscribers'] ?? 0,
      freeUsers: json['freeUsers'] ?? 0,
      recentUsers: json['recentUsers'] ?? 0,
      weeklyUsers: json['weeklyUsers'] ?? 0,
      totalRevenue: (json['totalRevenue'] ?? 0).toDouble(),
      monthlyRevenue: (json['monthlyRevenue'] as List? ?? [])
          .map((item) => MonthlyRevenue.fromJson(item))
          .toList(),
      serverStats: ServerStats.fromJson(json['serverStats'] ?? {}),
      userActivityStats: UserActivityStats.fromJson(json['userActivityStats'] ?? {}),
      growthRate: GrowthRate.fromJson(json['growthRate'] ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'totalUsers': totalUsers,
      'activeSubscribers': activeSubscribers,
      'freeUsers': freeUsers,
      'recentUsers': recentUsers,
      'weeklyUsers': weeklyUsers,
      'totalRevenue': totalRevenue,
      'monthlyRevenue': monthlyRevenue.map((item) => item.toJson()).toList(),
      'serverStats': serverStats.toJson(),
      'userActivityStats': userActivityStats.toJson(),
      'growthRate': growthRate.toJson(),
    };
  }
}

/// 월별 매출 통계
class MonthlyRevenue {
  final String month;
  final double revenue;
  final int subscribers;

  MonthlyRevenue({
    required this.month,
    required this.revenue,
    required this.subscribers,
  });

  factory MonthlyRevenue.fromJson(Map<String, dynamic> json) {
    return MonthlyRevenue(
      month: json['month'] ?? '',
      revenue: (json['revenue'] ?? 0).toDouble(),
      subscribers: json['subscribers'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'month': month,
      'revenue': revenue,
      'subscribers': subscribers,
    };
  }
}

/// 서버 통계
class ServerStats {
  final int totalFunctionCalls;
  final int totalStorageUsed;
  final int totalBandwidthUsed;
  final int averageResponseTime;
  final String errorRate;

  ServerStats({
    required this.totalFunctionCalls,
    required this.totalStorageUsed,
    required this.totalBandwidthUsed,
    required this.averageResponseTime,
    required this.errorRate,
  });

  factory ServerStats.fromJson(Map<String, dynamic> json) {
    return ServerStats(
      totalFunctionCalls: json['totalFunctionCalls'] ?? 0,
      totalStorageUsed: json['totalStorageUsed'] ?? 0,
      totalBandwidthUsed: json['totalBandwidthUsed'] ?? 0,
      averageResponseTime: json['averageResponseTime'] ?? 0,
      errorRate: json['errorRate'] ?? '0%',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'totalFunctionCalls': totalFunctionCalls,
      'totalStorageUsed': totalStorageUsed,
      'totalBandwidthUsed': totalBandwidthUsed,
      'averageResponseTime': averageResponseTime,
      'errorRate': errorRate,
    };
  }
}

/// 사용자 활동 통계
class UserActivityStats {
  final int dailyActiveUsers;
  final int weeklyActiveUsers;
  final int monthlyActiveUsers;
  final String averageSessionDuration;

  UserActivityStats({
    required this.dailyActiveUsers,
    required this.weeklyActiveUsers,
    required this.monthlyActiveUsers,
    required this.averageSessionDuration,
  });

  factory UserActivityStats.fromJson(Map<String, dynamic> json) {
    return UserActivityStats(
      dailyActiveUsers: json['dailyActiveUsers'] ?? 0,
      weeklyActiveUsers: json['weeklyActiveUsers'] ?? 0,
      monthlyActiveUsers: json['monthlyActiveUsers'] ?? 0,
      averageSessionDuration: json['averageSessionDuration'] ?? '0 minutes',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'dailyActiveUsers': dailyActiveUsers,
      'weeklyActiveUsers': weeklyActiveUsers,
      'monthlyActiveUsers': monthlyActiveUsers,
      'averageSessionDuration': averageSessionDuration,
    };
  }
}

/// 성장률 통계
class GrowthRate {
  final String weekly;
  final String monthly;

  GrowthRate({
    required this.weekly,
    required this.monthly,
  });

  factory GrowthRate.fromJson(Map<String, dynamic> json) {
    return GrowthRate(
      weekly: json['weekly'] ?? '0%',
      monthly: json['monthly'] ?? '0%',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'weekly': weekly,
      'monthly': monthly,
    };
  }
}

/// 관리자용 사용자 정보
class AdminUser {
  final String uid;
  final String? email;
  final String? nickname;
  final String? phone;
  final DateTime? createdAt;
  final AdminSubscription? subscription;
  final String? serverUsage;
  final int? avgDailyUsage;

  AdminUser({
    required this.uid,
    this.email,
    this.nickname,
    this.phone,
    this.createdAt,
    this.subscription,
    this.serverUsage,
    this.avgDailyUsage,
  });

  factory AdminUser.fromJson(Map<String, dynamic> json) {
    return AdminUser(
      uid: json['uid'] ?? '',
      email: json['email'],
      nickname: json['nickname'] ?? json['displayName'],
      phone: json['phone'],
      createdAt: json['createdAt'] != null 
        ? DateTime.parse(json['createdAt']) 
        : null,
      subscription: json['subscription'] != null 
        ? AdminSubscription.fromJson(json['subscription']) 
        : null,
      serverUsage: json['serverUsage'],
      avgDailyUsage: json['avgDailyUsage'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'uid': uid,
      'email': email,
      'nickname': nickname,
      'phone': phone,
      'createdAt': createdAt?.toIso8601String(),
      'subscription': subscription?.toJson(),
      'serverUsage': serverUsage,
      'avgDailyUsage': avgDailyUsage,
    };
  }
}

/// 관리자용 구독 정보
class AdminSubscription {
  final String status;
  final String? plan;
  final DateTime? nextPaymentDate;
  final DateTime? startedAt;
  final DateTime? lastPaymentDate;
  final String? paymentMethod;
  final int? price;
  final String? bid;

  AdminSubscription({
    required this.status,
    this.plan,
    this.nextPaymentDate,
    this.startedAt,
    this.lastPaymentDate,
    this.paymentMethod,
    this.price,
    this.bid,
  });

  factory AdminSubscription.fromJson(Map<String, dynamic> json) {
    return AdminSubscription(
      status: json['status'] ?? 'free',
      plan: json['plan'],
      nextPaymentDate: json['nextPaymentDate'] != null 
        ? DateTime.parse(json['nextPaymentDate']) 
        : null,
      startedAt: json['startedAt'] != null 
        ? DateTime.parse(json['startedAt']) 
        : null,
      lastPaymentDate: json['lastPaymentDate'] != null 
        ? DateTime.parse(json['lastPaymentDate']) 
        : null,
      paymentMethod: json['paymentMethod'],
      price: json['price'],
      bid: json['bid'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'status': status,
      'plan': plan,
      'nextPaymentDate': nextPaymentDate?.toIso8601String(),
      'startedAt': startedAt?.toIso8601String(),
      'lastPaymentDate': lastPaymentDate?.toIso8601String(),
      'paymentMethod': paymentMethod,
      'price': price,
      'bid': bid,
    };
  }

  bool get isActive => status == 'active';
}

/// 페이지네이션 정보
class AdminPagination {
  final int currentPage;
  final int totalPages;
  final int totalUsers;
  final bool hasNextPage;
  final bool hasPrevPage;

  AdminPagination({
    required this.currentPage,
    required this.totalPages,
    required this.totalUsers,
    required this.hasNextPage,
    required this.hasPrevPage,
  });

  factory AdminPagination.fromJson(Map<String, dynamic> json) {
    return AdminPagination(
      currentPage: json['currentPage'] ?? 1,
      totalPages: json['totalPages'] ?? 1,
      totalUsers: json['totalUsers'] ?? 0,
      hasNextPage: json['hasNextPage'] ?? false,
      hasPrevPage: json['hasPrevPage'] ?? false,
    );
  }

  factory AdminPagination.empty() {
    return AdminPagination(
      currentPage: 1,
      totalPages: 1,
      totalUsers: 0,
      hasNextPage: false,
      hasPrevPage: false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'currentPage': currentPage,
      'totalPages': totalPages,
      'totalUsers': totalUsers,
      'hasNextPage': hasNextPage,
      'hasPrevPage': hasPrevPage,
    };
  }
}

/// 사용자 목록 응답
class AdminUsersResponse {
  final List<AdminUser> users;
  final AdminPagination pagination;

  AdminUsersResponse({
    required this.users,
    required this.pagination,
  });

  factory AdminUsersResponse.fromJson(Map<String, dynamic> json) {
    return AdminUsersResponse(
      users: (json['users'] as List? ?? [])
          .map((user) => AdminUser.fromJson(user))
          .toList(),
      pagination: AdminPagination.fromJson(json['pagination'] ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'users': users.map((user) => user.toJson()).toList(),
      'pagination': pagination.toJson(),
    };
  }
}

/// 시스템 로그
class SystemLog {
  final DateTime timestamp;
  final String level;
  final String message;
  final String? details;

  SystemLog({
    required this.timestamp,
    required this.level,
    required this.message,
    this.details,
  });

  factory SystemLog.fromJson(Map<String, dynamic> json) {
    return SystemLog(
      timestamp: DateTime.parse(json['timestamp']),
      level: json['level'],
      message: json['message'],
      details: json['details'],
    );
  }
}

/// 시스템 통계
class SystemStats {
  final String totalFunctionCalls;
  final String successRate;
  final String lastExecution;

  SystemStats({
    required this.totalFunctionCalls,
    required this.successRate,
    required this.lastExecution,
  });

  factory SystemStats.fromJson(Map<String, dynamic> json) {
    return SystemStats(
      totalFunctionCalls: json['totalFunctionCalls'] ?? '-',
      successRate: json['successRate'] ?? '-',
      lastExecution: json['lastExecution'] ?? '-',
    );
  }
}

/// 관리자 로그인 응답
class AdminAuthResponse {
  final bool success;
  final String? token;
  final String? message;
  final String? expiresIn;

  AdminAuthResponse({
    required this.success,
    this.token,
    this.message,
    this.expiresIn,
  });

  factory AdminAuthResponse.fromJson(Map<String, dynamic> json) {
    return AdminAuthResponse(
      success: json['success'] ?? false,
      token: json['token'],
      message: json['message'],
      expiresIn: json['expiresIn'],
    );
  }
}
