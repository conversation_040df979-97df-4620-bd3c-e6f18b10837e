/// 로컬 데이터 폴백 Provider
/// 
/// 실시간 동기화가 실패할 경우 로컬 SQLite 데이터를 직접 로딩하는 폴백 시스템
/// 개선: Firestore 동기화 기능 추가로 다기기 동기화 지원

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../models/product.dart';
import '../models/seller.dart';
import '../models/prepayment.dart';
import '../models/sales_log.dart';
import '../models/set_discount.dart';
import '../repositories/product_repository.dart';
import '../repositories/seller_repository.dart';
import '../repositories/prepayment_repository.dart';
import '../repositories/sales_log_repository.dart';
import '../services/database_service.dart';
import '../providers/unified_workspace_provider.dart';
import '../providers/realtime_sync_provider.dart';
import '../utils/logger_utils.dart';

/// 폴백 데이터 상태
class FallbackDataState {
  final List<Product> products;
  final List<Seller> sellers;
  final List<Prepayment> prepayments;
  final List<SalesLog> salesLogs;
  final List<SetDiscount> setDiscounts;
  final bool isLoading;
  final String? errorMessage;

  FallbackDataState({
    this.products = const [],
    this.sellers = const [],
    this.prepayments = const [],
    this.salesLogs = const [],
    this.setDiscounts = const [],
    this.isLoading = false,
    this.errorMessage,
  });

  FallbackDataState copyWith({
    List<Product>? products,
    List<Seller>? sellers,
    List<Prepayment>? prepayments,
    List<SalesLog>? salesLogs,
    List<SetDiscount>? setDiscounts,
    bool? isLoading,
    String? errorMessage,
  }) {
    return FallbackDataState(
      products: products ?? this.products,
      sellers: sellers ?? this.sellers,
      prepayments: prepayments ?? this.prepayments,
      salesLogs: salesLogs ?? this.salesLogs,
      setDiscounts: setDiscounts ?? this.setDiscounts,
      isLoading: isLoading ?? this.isLoading,
      errorMessage: errorMessage,
    );
  }
}

/// 폴백 데이터 Provider
class FallbackDataNotifier extends StateNotifier<FallbackDataState> {
  static const String _tag = 'FallbackDataProvider';
  
  final Ref _ref;
  
  FallbackDataNotifier(this._ref) : super(FallbackDataState());

  /// 로컬 데이터를 직접 로딩
  Future<void> loadLocalData({int? eventId}) async {
    try {
      LoggerUtils.logInfo('폴백 데이터 로딩 시작', tag: _tag);
      
      state = state.copyWith(isLoading: true, errorMessage: null);
      
      // 현재 이벤트 ID 결정
      final currentWorkspace = _ref.read(currentWorkspaceProvider);
      final currentEventId = eventId ?? currentWorkspace?.id;
      if (currentEventId == null) {
        LoggerUtils.logWarning('이벤트 ID가 없어 폴백 데이터 로딩을 건너뜁니다', tag: _tag);
        state = state.copyWith(isLoading: false);
        return;
      }

      // Repository 인스턴스 생성
      final databaseService = _ref.read(databaseServiceProvider);
      final productRepo = ProductRepository(database: databaseService);
      final sellerRepo = SellerRepository(database: databaseService);
      final prepaymentRepo = PrepaymentRepository(database: databaseService);
      final salesLogRepo = SalesLogRepository(database: databaseService);

      // 병렬로 데이터 로딩 (세트 할인 제외)
      final results = await Future.wait([
        productRepo.getProductsByEventId(currentEventId),
        sellerRepo.getAllSellers(),
        prepaymentRepo.getPrepaymentsByEventId(currentEventId),
        salesLogRepo.getSalesLogsByEventId(currentEventId),
      ]);

      final products = results[0] as List<Product>;
      final sellers = results[1] as List<Seller>;
      final prepayments = results[2] as List<Prepayment>;
      final salesLogs = results[3] as List<SalesLog>;
      final setDiscounts = <SetDiscount>[]; // 임시로 빈 리스트

      state = state.copyWith(
        products: products,
        sellers: sellers,
        prepayments: prepayments,
        salesLogs: salesLogs,
        setDiscounts: setDiscounts,
        isLoading: false,
        errorMessage: null,
      );

      LoggerUtils.logInfo(
        '폴백 데이터 로딩 완료: 상품 ${products.length}개, 판매자 ${sellers.length}개, 선입금 ${prepayments.length}개',
        tag: _tag,
      );
    } catch (e, stackTrace) {
      LoggerUtils.logError('폴백 데이터 로딩 실패', tag: _tag, error: e, stackTrace: stackTrace);
      state = state.copyWith(
        isLoading: false,
        errorMessage: '로컬 데이터 로딩 실패: ${e.toString()}',
      );
    }
  }

  /// Firestore에서 데이터를 동기화하여 로컬에 저장
  Future<void> syncFromFirestore({int? eventId}) async {
    try {
      LoggerUtils.logInfo('Firestore 동기화 시작', tag: _tag);
      
      // 현재 사용자 확인
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) {
        LoggerUtils.logWarning('로그인되지 않아 Firestore 동기화를 건너뜁니다', tag: _tag);
        return;
      }

      // 현재 이벤트 ID 결정
      final currentWorkspace = _ref.read(currentWorkspaceProvider);
      final currentEventId = eventId ?? currentWorkspace?.id;
      if (currentEventId == null) {
        LoggerUtils.logWarning('이벤트 ID가 없어 Firestore 동기화를 건너뜁니다', tag: _tag);
        return;
      }

      state = state.copyWith(isLoading: true);
      
      final firestore = FirebaseFirestore.instance;
      final userId = user.uid;

      // Firestore에서 데이터 가져오기
      final eventDocRef = firestore.collection('users').doc(userId).collection('events').doc(currentEventId.toString());
      
      // 제품 데이터 가져오기
      final productsSnapshot = await eventDocRef.collection('products').get();
      
      // Repository 인스턴스 생성
      final databaseService = _ref.read(databaseServiceProvider);
      final productRepo = ProductRepository(database: databaseService);

      // Firestore 데이터를 로컬에 저장
      for (final doc in productsSnapshot.docs) {
        try {
          final productData = doc.data();
          productData['id'] = doc.id;
          final product = Product.fromJson(productData);
          await productRepo.insertProduct(product);
        } catch (e) {
          LoggerUtils.logWarning('제품 동기화 실패: ${doc.id}', tag: _tag);
        }
      }

      // 로컬 데이터 다시 로딩
      await loadLocalData(eventId: currentEventId);
      
      LoggerUtils.logInfo('Firestore 동기화 완료', tag: _tag);
    } catch (e, stackTrace) {
      LoggerUtils.logError('Firestore 동기화 실패', tag: _tag, error: e, stackTrace: stackTrace);
      // 동기화 실패시 로컬 데이터라도 로딩
      await loadLocalData(eventId: eventId);
    }
  }

  /// 데이터 로딩 (Firestore 동기화 시도 후 로컬 데이터 로딩)
  Future<void> loadData({int? eventId}) async {
    // 먼저 Firestore에서 동기화 시도
    await syncFromFirestore(eventId: eventId);
    // 동기화 실패해도 로컬 데이터는 이미 loadLocalData에서 로딩됨
  }

  /// 로컬 데이터를 Firestore에 업로드 (다기기 동기화용)
  Future<void> uploadToFirestore({int? eventId}) async {
    try {
      LoggerUtils.logInfo('Firestore 업로드 시작', tag: _tag);

      // 현재 사용자 확인
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) {
        LoggerUtils.logWarning('로그인되지 않아 Firestore 업로드를 건너뜁니다', tag: _tag);
        return;
      }

      // 실시간 동기화가 비활성화된 경우 업로드 건너뛰기
      final container = ProviderContainer();
      final realtimeSyncService = container.read(realtimeSyncServiceProvider);
      if (!realtimeSyncService.realtimeSyncEnabled) {
        LoggerUtils.logInfo('실시간 동기화가 비활성화되어 Firestore 업로드를 건너뜁니다', tag: _tag);
        container.dispose();
        return;
      }
      container.dispose();

      // 현재 이벤트 ID 결정
      final currentWorkspace = _ref.read(currentWorkspaceProvider);
      final currentEventId = eventId ?? currentWorkspace?.id;
      if (currentEventId == null) {
        LoggerUtils.logWarning('이벤트 ID가 없어 Firestore 업로드를 건너뜁니다', tag: _tag);
        return;
      }

      // 로컬 데이터 먼저 로딩
      await loadLocalData(eventId: currentEventId);
      
      final firestore = FirebaseFirestore.instance;
      final userId = user.uid;
      final eventDocRef = firestore.collection('users').doc(userId).collection('events').doc(currentEventId.toString());

      // 상품 데이터 업로드
      final products = state.products;
      final batch = firestore.batch();
      
      for (final product in products) {
        final productDoc = eventDocRef.collection('products').doc(product.id.toString());
        batch.set(productDoc, product.toJson(), SetOptions(merge: true));
      }

      await batch.commit();
      
      LoggerUtils.logInfo('Firestore 업로드 완료: 상품 ${products.length}개', tag: _tag);
    } catch (e, stackTrace) {
      LoggerUtils.logError('Firestore 업로드 실패', tag: _tag, error: e, stackTrace: stackTrace);
    }
  }

  /// 완전한 동기화 (업로드 + 다운로드)
  Future<void> fullSync({int? eventId}) async {
    // 1. 로컬 데이터를 Firestore에 업로드
    await uploadToFirestore(eventId: eventId);
    
    // 2. Firestore에서 최신 데이터 다운로드
    await syncFromFirestore(eventId: eventId);
  }
}

/// 폴백 데이터 Provider
final fallbackDataProvider = StateNotifierProvider<FallbackDataNotifier, FallbackDataState>((ref) {
  return FallbackDataNotifier(ref);
});

/// 폴백 상품 데이터 Provider
final fallbackProductsProvider = Provider<List<Product>>((ref) {
  return ref.watch(fallbackDataProvider).products;
});

/// 폴백 판매자 데이터 Provider
final fallbackSellersProvider = Provider<List<Seller>>((ref) {
  return ref.watch(fallbackDataProvider).sellers;
});

/// 폴백 선입금 데이터 Provider
final fallbackPrepaymentsProvider = Provider<List<Prepayment>>((ref) {
  return ref.watch(fallbackDataProvider).prepayments;
});
