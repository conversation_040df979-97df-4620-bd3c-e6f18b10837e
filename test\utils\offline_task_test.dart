import 'package:flutter_test/flutter_test.dart';
import 'package:parabara/utils/offline_task.dart';
import 'package:parabara/utils/network_status.dart';

void main() {
  group('오프라인 작업 기본 테스트', () {
    test('OfflineTask 생성 및 기본 속성 확인', () {
      final task = OfflineTask(
        id: 'test-1',
        type: OfflineTaskType.insert,
        table: 'products',
        data: {'name': '테스트 상품', 'price': 1000},
        timestamp: DateTime.now(),
      );

      expect(task.id, equals('test-1'));
      expect(task.type, equals(OfflineTaskType.insert));
      expect(task.table, equals('products'));
      expect(task.data['name'], equals('테스트 상품'));
      expect(task.data['price'], equals(1000));
    });

    test('OfflineTaskType enum 값 확인', () {
      expect(OfflineTaskType.insert.name, equals('insert'));
      expect(OfflineTaskType.update.name, equals('update'));
      expect(OfflineTaskType.delete.name, equals('delete'));
    });

    test('NetworkStatusUtil 기본 동작 확인', () {
      // 기본값 확인
      expect(NetworkStatusUtil.isOnline, isNotNull);
      
      // 온라인/오프라인 상태 설정
      NetworkStatusUtil.setOnline(true);
      expect(NetworkStatusUtil.isOnline, isTrue);
      
      NetworkStatusUtil.setOnline(false);
      expect(NetworkStatusUtil.isOnline, isFalse);
    });
  });
}
