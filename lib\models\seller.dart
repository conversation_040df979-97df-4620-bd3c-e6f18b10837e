import 'package:freezed_annotation/freezed_annotation.dart';

part 'seller.freezed.dart';
part 'seller.g.dart';

/// 판매자 정보를 표현하는 데이터 모델 클래스입니다.
/// - 판매자명, 고유 ID, 대표 판매자 여부 등 속성 포함
/// - DB 연동, CRUD, 필터/정렬/검색 등에서 사용
/// - freezed를 사용하여 불변 객체로 생성
@freezed
abstract class Seller with _$Seller {
  const factory Seller({
    int? id,
    required String name,
    @Default(false) bool isDefault,
    @Default(1) int eventId, // 행사 ID 추가
  }) = _Seller;

  factory Seller.fromJson(Map<String, dynamic> json) => _$SellerFromJson(json);

  // SQLite 맵에서 직접 생성
  factory Seller.fromMap(Map<String, dynamic> map) {
    return Seller(
      id: map['id'],
      name: map['name'] ?? '',
      isDefault: map['isDefault'] == 1 || map['isDefault'] == true,
      eventId: map['eventId'] as int? ?? 1, // 기본값 1
    );
  }

  // 현재 타임스탬프로 생성하는 팩토리
  factory Seller.create({int? id, required String name, bool isDefault = false, int eventId = 1}) {
    return Seller(id: id, name: name, isDefault: isDefault, eventId: eventId);
  }
}

// SQLite 맵 변환을 위한 Extension
extension SellerMapper on Seller {
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'isDefault': isDefault ? 1 : 0,
      'eventId': eventId,
    };
  }
}
