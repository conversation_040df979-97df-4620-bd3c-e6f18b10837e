import 'package:flutter/material.dart';
import '../../../models/admin_models.dart';

/// 사용자 테이블 위젯
class AdminUsersTable extends StatelessWidget {
  final List<AdminUser> users;
  final String searchQuery;
  final String statusFilter;
  final String sortBy;
  final String sortOrder;
  final Function(String) onSearchChanged;
  final Function(String) onStatusFilterChanged;
  final Function(String, String) onSortChanged;
  final Function(AdminUser) onToggleSubscription;
  final Function(AdminUser)? onViewDetails;
  final int pageSize;
  final int currentPage;
  final int totalPages;
  final int totalUsers;
  final Function(int)? onPageChanged;

  const AdminUsersTable({
    super.key,
    required this.users,
    required this.searchQuery,
    required this.statusFilter,
    required this.sortBy,
    required this.sortOrder,
    required this.onSearchChanged,
    required this.onStatusFilterChanged,
    required this.onSortChanged,
    required this.onToggleSubscription,
    this.onViewDetails,
    this.pageSize = 20,
    this.currentPage = 1,
    this.totalPages = 1,
    this.totalUsers = 0,
    this.onPageChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      color: Colors.white,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
        side: const BorderSide(color: Color(0xFFE9ECEF)),
      ),
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 헤더
            Row(
              children: [
                const Icon(
                  Icons.people,
                  color: Color(0xFF495057),
                ),
                const SizedBox(width: 8),
                const Text(
                  '사용자 목록',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: Color(0xFF495057),
                  ),
                ),
                const Spacer(),
                Text(
                  '총 $totalUsers명',
                  style: const TextStyle(
                    color: Color(0xFF6C757D),
                  ),
                ),
                if (onPageChanged != null) ...[
                  const SizedBox(width: 16),
                  _buildPagination(),
                ],
              ],
            ),
            const SizedBox(height: 24),

            // 검색 및 필터
            Row(
              children: [
                // 검색
                Expanded(
                  flex: 2,
                  child: TextField(
                    decoration: InputDecoration(
                      hintText: '이메일, 닉네임, 전화번호로 검색...',
                      prefixIcon: const Icon(Icons.search, color: Color(0xFF6C757D)),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(4),
                        borderSide: const BorderSide(color: Color(0xFFCED4DA)),
                      ),
                      enabledBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(4),
                        borderSide: const BorderSide(color: Color(0xFFCED4DA)),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(4),
                        borderSide: const BorderSide(color: Color(0xFF495057)),
                      ),
                      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                    ),
                    onChanged: onSearchChanged,
                  ),
                ),
                const SizedBox(width: 16),

                // 상태 필터
                SizedBox(
                  width: 150,
                  child: DropdownButtonFormField<String>(
                    value: statusFilter,
                    decoration: InputDecoration(
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(4),
                        borderSide: const BorderSide(color: Color(0xFFCED4DA)),
                      ),
                      enabledBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(4),
                        borderSide: const BorderSide(color: Color(0xFFCED4DA)),
                      ),
                      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                    ),
                    items: const [
                      DropdownMenuItem(value: 'all', child: Text('모든 사용자')),
                      DropdownMenuItem(value: 'active', child: Text('프로 플랜')),
                      DropdownMenuItem(value: 'free', child: Text('무료 플랜')),
                    ],
                    onChanged: (value) => onStatusFilterChanged(value ?? 'all'),
                  ),
                ),
                const SizedBox(width: 16),

                // 정렬
                SizedBox(
                  width: 120,
                  child: DropdownButtonFormField<String>(
                    value: sortBy,
                    decoration: InputDecoration(
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(4),
                        borderSide: const BorderSide(color: Color(0xFFCED4DA)),
                      ),
                      enabledBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(4),
                        borderSide: const BorderSide(color: Color(0xFFCED4DA)),
                      ),
                      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                    ),
                    items: const [
                      DropdownMenuItem(value: 'createdAt', child: Text('가입일순')),
                      DropdownMenuItem(value: 'email', child: Text('이메일순')),
                      DropdownMenuItem(value: 'nickname', child: Text('닉네임순')),
                    ],
                    onChanged: (value) => onSortChanged(value ?? 'createdAt', sortOrder),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 24),

            // 테이블
            Container(
              decoration: BoxDecoration(
                border: Border.all(color: const Color(0xFFE9ECEF)),
                borderRadius: BorderRadius.circular(4),
              ),
              child: Column(
                children: [
                  // 헤더
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: const BoxDecoration(
                      color: Color(0xFFF8F9FA),
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(4),
                        topRight: Radius.circular(4),
                      ),
                    ),
                    child: const Row(
                      children: [
                        SizedBox(width: 80, child: Center(child: Text('UID', style: TextStyle(fontWeight: FontWeight.w600)))),
                        SizedBox(width: 200, child: Center(child: Text('이메일', style: TextStyle(fontWeight: FontWeight.w600)))),
                        SizedBox(width: 120, child: Center(child: Text('닉네임', style: TextStyle(fontWeight: FontWeight.w600)))),
                        SizedBox(width: 120, child: Center(child: Text('전화번호', style: TextStyle(fontWeight: FontWeight.w600)))),
                        SizedBox(width: 100, child: Center(child: Text('구독 상태', style: TextStyle(fontWeight: FontWeight.w600)))),
                        SizedBox(width: 120, child: Center(child: Text('다음 결제일', style: TextStyle(fontWeight: FontWeight.w600)))),
                        SizedBox(width: 100, child: Center(child: Text('서버 사용량', style: TextStyle(fontWeight: FontWeight.w600)))),
                        SizedBox(width: 180, child: Center(child: Text('작업', style: TextStyle(fontWeight: FontWeight.w600)))),
                      ],
                    ),
                  ),

                  // 데이터 행들
                  if (users.isEmpty)
                    const Padding(
                      padding: EdgeInsets.all(32),
                      child: Center(
                        child: Text(
                          '사용자가 없습니다.',
                          style: TextStyle(color: Color(0xFF6C757D)),
                        ),
                      ),
                    )
                  else
                    ...users.map((user) => _buildUserRow(user)),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildUserRow(AdminUser user) {
    final isActive = user.subscription?.isActive ?? false;
    
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: const BoxDecoration(
        border: Border(
          bottom: BorderSide(color: Color(0xFFE9ECEF)),
        ),
      ),
      child: Row(
        children: [
          // UID
          SizedBox(
            width: 80,
            child: Center(
              child: Text(
                user.uid.length > 8 ? '${user.uid.substring(0, 8)}...' : user.uid,
                style: const TextStyle(
                  fontFamily: 'monospace',
                  fontSize: 12,
                  color: Color(0xFF6C757D),
                ),
              ),
            ),
          ),

          // 이메일
          SizedBox(
            width: 200,
            child: Center(
              child: Text(
                user.email ?? 'N/A',
                style: const TextStyle(fontSize: 14),
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ),

          // 닉네임
          SizedBox(
            width: 120,
            child: Center(
              child: Text(
                user.nickname ?? 'N/A',
                style: const TextStyle(fontSize: 14),
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ),

          // 전화번호
          SizedBox(
            width: 120,
            child: Center(
              child: Text(
                user.phone ?? 'N/A',
                style: const TextStyle(fontSize: 14),
              ),
            ),
          ),

          // 구독 상태
          SizedBox(
            width: 100,
            child: Center(
              child: Text(
                isActive ? '프로 플랜' : '무료 플랜',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: isActive ? const Color(0xFF10B981) : const Color(0xFF6C757D),
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ),

          // 다음 결제일
          SizedBox(
            width: 120,
            child: Center(
              child: Text(
                user.subscription?.nextPaymentDate != null
                    ? '${user.subscription!.nextPaymentDate!.month}/${user.subscription!.nextPaymentDate!.day}'
                    : 'N/A',
                style: const TextStyle(fontSize: 14),
              ),
            ),
          ),

          // 서버 사용량
          SizedBox(
            width: 100,
            child: Center(
              child: Text(
                user.serverUsage ?? '데이터 없음',
                style: const TextStyle(
                  fontSize: 12,
                  color: Color(0xFF6C757D),
                ),
              ),
            ),
          ),

          // 작업 버튼들
          SizedBox(
            width: 180,
            child: Center(
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  if (onViewDetails != null)
                    ElevatedButton(
                      onPressed: () => onViewDetails!(user),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: const Color(0xFF6366F1),
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
                        minimumSize: const Size(60, 32),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(4),
                        ),
                      ),
                      child: const Text(
                        '상세보기',
                        style: TextStyle(fontSize: 11, fontWeight: FontWeight.w600),
                      ),
                    ),
                  const SizedBox(width: 8),
                  ElevatedButton(
                    onPressed: () => onToggleSubscription(user),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: isActive ? const Color(0xFFDC3545) : const Color(0xFF10B981),
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
                      minimumSize: const Size(60, 32),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(4),
                      ),
                    ),
                    child: Text(
                      isActive ? '해제' : '활성화',
                      style: const TextStyle(fontSize: 11, fontWeight: FontWeight.w600),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPagination() {
    if (totalPages <= 1) return const SizedBox.shrink();

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: const Color(0xFFE9ECEF)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          IconButton(
            onPressed: currentPage > 1 ? () => onPageChanged!(currentPage - 1) : null,
            icon: const Icon(Icons.chevron_left),
            iconSize: 20,
            color: currentPage > 1 ? const Color(0xFF495057) : Colors.grey,
          ),
          const SizedBox(width: 8),
          Text(
            '$currentPage / $totalPages',
            style: const TextStyle(
              fontSize: 14,
              color: Color(0xFF495057),
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(width: 8),
          IconButton(
            onPressed: currentPage < totalPages ? () => onPageChanged!(currentPage + 1) : null,
            icon: const Icon(Icons.chevron_right),
            iconSize: 20,
            color: currentPage < totalPages ? const Color(0xFF495057) : Colors.grey,
          ),
        ],
      ),
    );
  }
}
