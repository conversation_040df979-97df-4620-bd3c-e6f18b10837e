import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../../utils/app_colors.dart';
import '../../utils/logger_utils.dart';

/// 전화번호 인증 화면
class PhoneVerificationScreen extends StatefulWidget {
  const PhoneVerificationScreen({super.key});

  @override
  State<PhoneVerificationScreen> createState() => _PhoneVerificationScreenState();
}

class _PhoneVerificationScreenState extends State<PhoneVerificationScreen> {
  // 웹뷰 관련
  WebViewController? _webViewController;
  
  @override
  void initState() {
    super.initState();
    _initializeWebView();
  }

  /// 웹뷰 초기화
  void _initializeWebView() {
    _webViewController = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setNavigationDelegate(
        NavigationDelegate(
          onPageFinished: (String url) async {
            LoggerUtils.logInfo('전화번호 인증 웹뷰 로드 완료: $url');

            // 페이지 로드 완료 후 사용자 UID 전달
            final user = FirebaseAuth.instance.currentUser;
            if (user != null) {
              await _webViewController!.runJavaScript(
                'window.setUserInfo("${user.uid}");'
              );
              LoggerUtils.logInfo('웹뷰에 사용자 UID 전달 완료: ${user.uid}');
            }
          },
          onWebResourceError: (WebResourceError error) {
            LoggerUtils.logError('웹뷰 로드 오류: ${error.description}');
          },
        ),
      )
      ..addJavaScriptChannel(
        'phoneVerificationSuccess',
        onMessageReceived: (JavaScriptMessage message) {
          LoggerUtils.logInfo('전화번호 인증 성공: ${message.message}');
          _handleVerificationSuccess(message.message);
        },
      )
      ..loadRequest(Uri.parse('https://parabara-1a504.web.app/phone-verification.html?nocache=${DateTime.now().millisecondsSinceEpoch}'));
  }

  /// 전화번호 인증 성공 처리
  void _handleVerificationSuccess(String message) {
    try {
      LoggerUtils.logInfo('전화번호 인증 메시지 수신: $message');

      // JSON 파싱
      final Map<String, dynamic> data = json.decode(message);
      final String? action = data['action'] as String?;
      final bool verified = data['verified'] as bool? ?? false;

      if (verified) {
        LoggerUtils.logInfo('전화번호 인증 완료: ${data['phoneNumber']}');

        // 웹뷰 자동 종료 요청이 있거나 인증이 완료된 경우
        if (action == 'close' || verified) {
          if (mounted) {
            Navigator.of(context).pop(true); // 성공 결과 반환

            // 성공 다이얼로그 표시
            _showSuccessDialog();
          }
        }
      }
    } catch (e) {
      LoggerUtils.logError('전화번호 인증 결과 처리 오류', error: e);
      // JSON 파싱 실패 시에도 성공으로 처리 (이전 방식 호환)
      if (mounted) {
        Navigator.of(context).pop(true);
        _showSuccessDialog();
      }
    }
  }

  /// 인증 성공 다이얼로그 표시
  void _showSuccessDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.check_circle, color: Colors.green, size: 28),
            SizedBox(width: 12),
            Text('인증 완료'),
          ],
        ),
        content: const Text('전화번호 인증이 완료되었습니다!\n이제 구독 서비스를 이용하실 수 있습니다.'),
        actions: [
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primarySeed,
              foregroundColor: Colors.white,
            ),
            child: const Text('확인'),
          ),
        ],
      ),
    );
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('전화번호 인증'),
        backgroundColor: AppColors.primarySeed,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: SafeArea(
        child: WebViewWidget(
          controller: _webViewController!,
        ),
      ),
    );
  }
}
