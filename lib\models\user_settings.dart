/// 바라 부스 매니저 - 사용자 설정 데이터 모델
///
/// 사용자별 설정 정보를 표현하는 데이터 모델 클래스입니다.
/// - 마지막 선택된 워크스페이스 ID
/// - 기타 사용자 개인 설정들
/// - 실시간 동기화 지원
/// - 불변 객체 패턴 (immutable)
///
/// 작성자: Blue
/// 버전: 1.0.0
/// 최종 업데이트: 2025년 7월
library;

import 'package:freezed_annotation/freezed_annotation.dart';
import 'subscription_plan.dart';

part 'user_settings.freezed.dart';
part 'user_settings.g.dart';

/// 사용자 설정 정보를 담는 모델 클래스입니다.
/// freezed를 사용하여 불변 객체로 생성
@freezed
abstract class UserSettings with _$UserSettings {
  const factory UserSettings({
    /// 마지막으로 선택된 워크스페이스 ID
    int? lastWorkspaceId,

    /// 설정 업데이트 시간
    @Default(null) DateTime? updatedAt,

    /// 기기 ID (어느 기기에서 마지막으로 변경되었는지 추적)
    String? deviceId,

    /// 실시간 동기화 활성화 여부 (기본값: false)
    @Default(false) bool realtimeSyncEnabled,

    /// 구독 플랜 타입 (기본값: 무료 플랜)
    @Default(SubscriptionPlanType.free) SubscriptionPlanType subscriptionPlanType,
  }) = _UserSettings;

  factory UserSettings.fromJson(Map<String, dynamic> json) => _$UserSettingsFromJson(json);

  /// 기본 사용자 설정 생성
  factory UserSettings.createDefault() {
    return const UserSettings(
      lastWorkspaceId: null,
      updatedAt: null,
      deviceId: null,
      realtimeSyncEnabled: false,
      subscriptionPlanType: SubscriptionPlanType.free,
    );
  }
}

extension UserSettingsExtensions on UserSettings {
  /// Firebase 저장용 Map 변환
  Map<String, dynamic> toFirebaseMap() {
    return {
      'lastWorkspaceId': lastWorkspaceId,
      'updatedAt': updatedAt?.toIso8601String(),
      'deviceId': deviceId,
      'realtimeSyncEnabled': realtimeSyncEnabled,
      'subscriptionPlanType': this.subscriptionPlanType.toString(),
    };
  }

  /// Firebase에서 로드한 데이터로부터 UserSettings 생성
  static UserSettings fromFirebaseMap(Map<String, dynamic> map) {
    return UserSettings(
      lastWorkspaceId: map['lastWorkspaceId'] as int?,
      updatedAt: map['updatedAt'] != null
          ? DateTime.parse(map['updatedAt'] as String)
          : null,
      deviceId: map['deviceId'] as String?,
      realtimeSyncEnabled: map['realtimeSyncEnabled'] as bool? ?? false, // 기본값 false로 변경
      subscriptionPlanType: map['subscriptionPlanType'] != null
          ? SubscriptionPlanType.values.firstWhere(
              (e) => e.toString() == map['subscriptionPlanType'],
              orElse: () => SubscriptionPlanType.free,
            )
          : SubscriptionPlanType.free,
    );
  }

  /// 워크스페이스 변경으로 업데이트된 설정 생성
  UserSettings withWorkspaceUpdate({
    required int? workspaceId,
    String? deviceId,
  }) {
    return copyWith(
      lastWorkspaceId: workspaceId,
      updatedAt: DateTime.now(),
      deviceId: deviceId,
    );
  }

  /// 실시간 동기화 설정 변경으로 업데이트된 설정 생성
  UserSettings withRealtimeSyncUpdate({
    required bool enabled,
    String? deviceId,
  }) {
    return copyWith(
      realtimeSyncEnabled: enabled,
      updatedAt: DateTime.now(),
      deviceId: deviceId,
    );
  }

  /// 설정이 유효한지 확인
  bool get isValid => lastWorkspaceId != null && lastWorkspaceId! > 0;

  /// 구독 플랜 변경으로 업데이트된 설정 생성
  UserSettings withSubscriptionUpdate({
    required SubscriptionPlanType planType,
    String? deviceId,
  }) {
    return copyWith(
      subscriptionPlanType: planType,
      updatedAt: DateTime.now(),
      deviceId: deviceId,
    );
  }

  /// 현재 구독 플랜 정보 가져오기
  SubscriptionPlan get currentPlan => PredefinedPlans.getPlan(subscriptionPlanType);

  /// 프로 플랜 사용자인지 확인
  bool get isProUser => subscriptionPlanType == SubscriptionPlanType.pro;

  /// 무료 플랜 사용자인지 확인
  bool get isFreeUser => subscriptionPlanType == SubscriptionPlanType.free;
}
