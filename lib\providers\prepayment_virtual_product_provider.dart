import 'dart:async';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/prepayment_virtual_product.dart';
import '../models/event_workspace.dart';
import '../services/database_service.dart';
import '../services/realtime_sync_service_main.dart';
import '../providers/realtime_sync_provider.dart';
import '../utils/logger_utils.dart';
import 'unified_workspace_provider.dart';
import '../utils/event_workspace_utils.dart';
import 'prepayment_product_link_provider.dart';


// 선입금 가상 상품 데이터 상태
class PrepaymentVirtualProductState {
  final List<PrepaymentVirtualProduct> virtualProducts;
  final bool isLoading;
  final String? error;

  const PrepaymentVirtualProductState({
    this.virtualProducts = const [],
    this.isLoading = false,
    this.error,
  });

  PrepaymentVirtualProductState copyWith({
    List<PrepaymentVirtualProduct>? virtualProducts,
    bool? isLoading,
    String? error,
  }) {
    return PrepaymentVirtualProductState(
      virtualProducts: virtualProducts ?? this.virtualProducts,
      isLoading: isLoading ?? this.isLoading,
      error: error ?? this.error,
    );
  }
}

// 선입금 가상 상품 데이터 Notifier
class PrepaymentVirtualProductNotifier extends StateNotifier<PrepaymentVirtualProductState> {
  final Ref _ref;
  final DatabaseService _databaseService;
  StreamSubscription<RealtimeDataChange>? _realtimeSubscription;

  // 무한 루프 방지를 위한 최근 추가한 가상 상품 캐시
  final Set<int> _recentlyAddedVirtualProducts = <int>{};

  PrepaymentVirtualProductNotifier(this._ref, this._databaseService)
      : super(const PrepaymentVirtualProductState()) {
    // 선입금 가상 상품은 모든 플랜에서 로컬 사용 가능
    _setupRealtimeSync();
  }



  /// 실시간 동기화 설정
  void _setupRealtimeSync() {
    try {
      // 기존 구독 해제
      _realtimeSubscription?.cancel();

      final currentWorkspace = _ref.read(currentWorkspaceProvider);
      if (currentWorkspace == null) {
        LoggerUtils.logWarning('현재 워크스페이스가 없어 실시간 동기화를 설정할 수 없습니다', tag: 'PrepaymentVirtualProductNotifier');
        return;
      }

      // 실시간 동기화 서비스 가져오기
      final realtimeService = _ref.read(realtimeSyncServiceProvider);

      LoggerUtils.logInfo('선입금 가상 상품 실시간 데이터 스트림 구독 시작', tag: 'PrepaymentVirtualProductNotifier');

      // 데이터 변경 리스너 설정
      _realtimeSubscription = realtimeService.dataChanges.listen((change) {
        _handleRealtimeDataChange(change);
      });
    } catch (e) {
      LoggerUtils.logError('실시간 동기화 설정 실패', tag: 'PrepaymentVirtualProductNotifier', error: e);
    }
  }

  /// 실시간 데이터 변경 처리
  void _handleRealtimeDataChange(RealtimeDataChange change) {
    try {
      final currentWorkspace = _ref.read(currentWorkspaceProvider);

      // 현재 워크스페이스의 선입금 가상 상품 변경인지 확인
      if (currentWorkspace?.id == change.eventId && change.collectionName == 'prepayment_virtual_products') {
        final virtualProductId = int.tryParse(change.documentId);

        // 자기가 최근에 추가한 가상 상품은 무시 (무한 루프 방지)
        if (virtualProductId != null && _recentlyAddedVirtualProducts.contains(virtualProductId)) {
          LoggerUtils.logDebug('최근 추가한 가상 상품 무시: ID $virtualProductId', tag: 'PrepaymentVirtualProductNotifier');
          return;
        }

        LoggerUtils.logInfo('선입금 가상 상품 실시간 변경 감지: ${change.changeType.name} - ${change.documentId}', tag: 'PrepaymentVirtualProductNotifier');

        // 가상 상품 목록 즉시 새로고침 (로딩 없이)
        loadVirtualProducts();
      }
    } catch (e) {
      LoggerUtils.logError('실시간 데이터 변경 처리 실패', tag: 'PrepaymentVirtualProductNotifier', error: e);
    }
  }

  @override
  void dispose() {
    _realtimeSubscription?.cancel();
    super.dispose();
  }

  // 모든 선입금에서 사용된 가상 상품 데이터를 로드
  Future<void> loadVirtualProducts({bool showLoading = true}) async {
    if (showLoading) {
      state = state.copyWith(isLoading: true);
    }
    int retry = 0;
    while (retry < 3) {
      try {
        final products = await _loadVirtualProductsFromDatabase();
        state = state.copyWith(virtualProducts: products, isLoading: false, error: null);
        return;
      } catch (e) {
        final msg = e.toString();
        if (msg.contains('no such table') || msg.contains('database is not open')) {
          retry++;
          await Future.delayed(const Duration(milliseconds: 500));
          continue;
        } else {
          state = state.copyWith(isLoading: false, error: msg);
          return;
        }
      }
    }
    state = state.copyWith(isLoading: false, error: '데이터베이스 초기화 중입니다. 잠시 후 다시 시도해주세요.');
  }

  // DB에서 가상 상품 데이터를 로드 (현재 행사 기준)
  Future<List<PrepaymentVirtualProduct>> _loadVirtualProductsFromDatabase() async {
    try {
      // 현재 선택된 행사 워크스페이스 확인
      EventWorkspace? currentWorkspace = _ref.read(currentWorkspaceProvider);

      if (currentWorkspace == null) {
        LoggerUtils.logWarning('현재 선택된 워크스페이스가 없습니다', tag: 'PrepaymentVirtualProductNotifier');
        return [];
      }

      // DatabaseService 사용
      final db = await _databaseService.database;

      // 테이블 존재 여부 확인
      final tables = await db.query('sqlite_master', where: 'type = ? AND name = ?', whereArgs: ['table', 'prepayment_virtual_product']);
      if (tables.isEmpty) {
        LoggerUtils.logInfo('prepayment_virtual_product 테이블이 존재하지 않음', tag: 'PrepaymentVirtualProductNotifier');
        return [];
      }

      // 현재 행사의 가상 상품 데이터만 조회
      final List<Map<String, dynamic>> maps = await db.query(
        'prepayment_virtual_product',
        where: 'eventId = ?',
        whereArgs: [currentWorkspace.id],
      );

      final virtualProducts = maps.map((map) {
        return PrepaymentVirtualProduct(
          id: map['id'] as int,
          name: map['name'] as String,
          price: (map['price'] as num).toDouble(),
          quantity: map['quantity'] as int,
          createdAt: DateTime.parse(map['createdAt'] as String),
          updatedAt: map['updatedAt'] != null ? DateTime.parse(map['updatedAt'] as String) : null,
          eventId: map['eventId'] as int? ?? currentWorkspace.id,
        );
      }).toList();

      LoggerUtils.logInfo('DB에서 현재 워크스페이스(${currentWorkspace.name})의 가상 상품 데이터 로드 완료: ${virtualProducts.length}개', tag: 'PrepaymentVirtualProductNotifier');
      return virtualProducts;
    } catch (e) {
      LoggerUtils.logError('DB에서 가상 상품 데이터 로드 중 오류', error: e, tag: 'PrepaymentVirtualProductNotifier');
      return [];
    }
  }

  // 가상 상품 데이터 추가
  Future<void> addVirtualProduct(String name, int quantity) async {
  final normalized = name.trim().toLowerCase();
  // 이미 존재하는 상품명인지 확인 (소문자 비교)
  final existingIndex = state.virtualProducts.indexWhere((p) => p.name.toLowerCase() == normalized);

    if (existingIndex >= 0) {
      // 기존 상품이면 수량을 추가
      final updatedProducts = List<PrepaymentVirtualProduct>.from(state.virtualProducts);
      final existingProduct = updatedProducts[existingIndex];
      final updatedProduct = PrepaymentVirtualProduct(
        id: existingProduct.id,
        name: existingProduct.name, // 원본 보존
        price: existingProduct.price,
        quantity: existingProduct.quantity + quantity,
        createdAt: existingProduct.createdAt,
        updatedAt: DateTime.now(),
        eventId: existingProduct.eventId,
      );
      updatedProducts[existingIndex] = updatedProduct;
      state = state.copyWith(virtualProducts: updatedProducts);

      // 로컬 DB 반영 (업서트)
      try {
        await _databaseService.insertOrUpdatePrepaymentVirtualProduct(updatedProduct);
      } catch (e) {
        LoggerUtils.logError('가상 상품 로컬 DB 업데이트 실패: ${updatedProduct.name}', tag: 'PrepaymentVirtualProductNotifier', error: e);
      }

      // Firebase에 업데이트
      try {
        final realtimeService = _ref.read(realtimeSyncServiceProvider);
        await realtimeService.updatePrepaymentVirtualProduct(updatedProduct.eventId, updatedProduct);
        LoggerUtils.logInfo('가상 상품 Firebase 업데이트 성공: ${updatedProduct.name}', tag: 'PrepaymentVirtualProductNotifier');
      } catch (e) {
        LoggerUtils.logError('가상 상품 Firebase 업데이트 실패: ${updatedProduct.name}', tag: 'PrepaymentVirtualProductNotifier', error: e);
      }
    } else {
      // 새 상품이면 추가
      final currentWorkspace = _ref.read(currentWorkspaceProvider);
      final currentEvent = EventWorkspaceUtils.workspaceToEvent(currentWorkspace);
      if (currentEvent == null) return;

      final newProduct = PrepaymentVirtualProduct(
        id: DateTime.now().millisecondsSinceEpoch,
        name: normalized,
        price: 0, // 기본 가격 0으로 설정
        quantity: quantity,
        createdAt: DateTime.now(),
        eventId: currentEvent.id!,
      );

      // 최근 추가한 가상 상품으로 캐시 (무한 루프 방지용)
      _recentlyAddedVirtualProducts.add(newProduct.id);
      // 5초 후 캐시에서 제거
      Future.delayed(const Duration(seconds: 5), () {
        _recentlyAddedVirtualProducts.remove(newProduct.id);
      });

      state = state.copyWith(
        virtualProducts: [newProduct, ...state.virtualProducts],
      );

      // 로컬 DB 반영 (신규 삽입)
      try {
        await _databaseService.insertOrUpdatePrepaymentVirtualProduct(newProduct);
      } catch (e) {
        LoggerUtils.logError('가상 상품 로컬 DB 삽입 실패: ${newProduct.name}', tag: 'PrepaymentVirtualProductNotifier', error: e);
      }

      // Firebase에 추가
      try {
        final realtimeService = _ref.read(realtimeSyncServiceProvider);
        await realtimeService.addPrepaymentVirtualProduct(newProduct.eventId, newProduct);
        LoggerUtils.logInfo('가상 상품 Firebase 추가 성공: ${newProduct.name}', tag: 'PrepaymentVirtualProductNotifier');
      } catch (e) {
        LoggerUtils.logError('가상 상품 Firebase 추가 실패: ${newProduct.name}', tag: 'PrepaymentVirtualProductNotifier', error: e);
      }
    }
  }

  // 가상 상품 데이터 삭제 (완전 삭제)
  void removeVirtualProduct(String name) {
    state = state.copyWith(
      virtualProducts: state.virtualProducts.where((p) => p.name != name).toList(),
    );
  }

  /// 이름으로 단일 가상 상품 완전 삭제 (로컬 + Firebase)
  Future<void> deleteVirtualProductByName(String name) async {
    final index = state.virtualProducts.indexWhere((p) => p.name == name);
    if (index < 0) return;
    final product = state.virtualProducts[index];
    // 로컬 상태 제거
    final updated = List<PrepaymentVirtualProduct>.from(state.virtualProducts)..removeAt(index);
    state = state.copyWith(virtualProducts: updated);
    // 로컬/원격 삭제
    await _deleteVirtualProductCompletely(product);
  }

  /// 다중 ID 일괄 삭제 (로컬 + Firebase batch)
  Future<void> batchDeleteVirtualProducts(List<int> ids) async {
    if (ids.isEmpty) return;
    final toDelete = state.virtualProducts.where((p) => ids.contains(p.id)).toList();
    if (toDelete.isEmpty) return;
    // 상태 먼저 제거
    state = state.copyWith(
      virtualProducts: state.virtualProducts.where((p) => !ids.contains(p.id)).toList(),
    );
    // 로컬 DB 삭제 + Firebase batch 삭제
    try {
      final db = await _databaseService.database;
      final batch = db.batch();
      for (final p in toDelete) {
        batch.delete('prepayment_virtual_product', where: 'id = ?', whereArgs: [p.id]);
      }
      await batch.commit(noResult: true);
    } catch (e) {
      LoggerUtils.logError('가상 상품 로컬 배치 삭제 실패', tag: 'PrepaymentVirtualProductNotifier', error: e);
    }
    try {
      final realtimeService = _ref.read(realtimeSyncServiceProvider);
      final eventId = toDelete.first.eventId; // 모두 동일하다고 가정
      await realtimeService.batchDeletePrepaymentVirtualProducts(eventId, toDelete.map((e) => e.id).toList());
    } catch (e) {
      LoggerUtils.logError('가상 상품 Firebase 배치 삭제 실패', tag: 'PrepaymentVirtualProductNotifier', error: e);
    }

    // 연동 관계 정리 (가상 상품 관련 링크 제거)
    try {
      await _ref.read(prepaymentProductLinkNotifierProvider.notifier).removeLinksByVirtualProductIds(ids);
    } catch (e) {
      LoggerUtils.logError('가상 상품 연동 일괄 제거 실패', tag: 'PrepaymentVirtualProductNotifier', error: e);
    }
  }

  // 가상 상품 데이터 수량 차감 (선입금 삭제 시 사용)
  void subtractVirtualProductQuantity(String name, int quantity) {
  final normalized = name.trim().toLowerCase();
  final existingIndex = state.virtualProducts.indexWhere((p) => p.name.toLowerCase() == normalized);
    
    if (existingIndex >= 0) {
      final updatedProducts = List<PrepaymentVirtualProduct>.from(state.virtualProducts);
      final existingProduct = updatedProducts[existingIndex];
      final newQuantity = existingProduct.quantity - quantity;
      
      if (newQuantity <= 0) {
        // 수량이 0 이하면 완전 삭제
        updatedProducts.removeAt(existingIndex);
        // 로컬 DB 삭제 & Firebase 삭제
        _deleteVirtualProductCompletely(existingProduct);
      } else {
        // 수량만 차감
        final reduced = PrepaymentVirtualProduct(
          id: existingProduct.id,
          name: name,
          price: existingProduct.price,
          quantity: newQuantity,
          createdAt: existingProduct.createdAt,
          updatedAt: DateTime.now(),
          eventId: existingProduct.eventId,
        );
        updatedProducts[existingIndex] = reduced;
        _upsertReducedVirtualProduct(reduced);
      }
      
      state = state.copyWith(virtualProducts: updatedProducts);
    }
  }

  /// 내부: 수량 감소 후 로컬/원격 업서트
  Future<void> _upsertReducedVirtualProduct(PrepaymentVirtualProduct product) async {
    try {
      await _databaseService.insertOrUpdatePrepaymentVirtualProduct(product);
    } catch (e) {
      LoggerUtils.logError('가상 상품 로컬 DB 수량 차감 업서트 실패: ${product.name}', tag: 'PrepaymentVirtualProductNotifier', error: e);
    }
    try {
      final realtimeService = _ref.read(realtimeSyncServiceProvider);
      await realtimeService.updatePrepaymentVirtualProduct(product.eventId, product);
    } catch (e) {
      LoggerUtils.logError('가상 상품 Firebase 수량 차감 업데이터 실패: ${product.name}', tag: 'PrepaymentVirtualProductNotifier', error: e);
    }
  }

  /// 내부: 완전 삭제 (로컬 DB + Firebase)
  Future<void> _deleteVirtualProductCompletely(PrepaymentVirtualProduct product) async {
    try {
      final db = await _databaseService.database;
      await db.delete('prepayment_virtual_product', where: 'id = ?', whereArgs: [product.id]);
    } catch (e) {
      LoggerUtils.logError('가상 상품 로컬 DB 삭제 실패: ${product.name}', tag: 'PrepaymentVirtualProductNotifier', error: e);
    }
    try {
      final realtimeService = _ref.read(realtimeSyncServiceProvider);
      await realtimeService.deletePrepaymentVirtualProduct(product.eventId, product.id);
    } catch (e) {
      LoggerUtils.logError('가상 상품 Firebase 삭제 실패: ${product.name}', tag: 'PrepaymentVirtualProductNotifier', error: e);
    }
  }

  // 가상 상품 데이터 검색
  List<PrepaymentVirtualProduct> searchVirtualProducts(String query) {
    if (query.isEmpty) return state.virtualProducts;
    
    return state.virtualProducts
        .where((product) => product.name.toLowerCase().contains(query.toLowerCase()))
        .toList();
  }
}

// Provider 정의
final prepaymentVirtualProductNotifierProvider = StateNotifierProvider<
    PrepaymentVirtualProductNotifier, PrepaymentVirtualProductState>((ref) {
  final databaseService = ref.watch(databaseServiceProvider);
  return PrepaymentVirtualProductNotifier(ref, databaseService);
});
