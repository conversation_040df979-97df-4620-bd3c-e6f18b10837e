// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'event_invitation.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_EventInvitation _$EventInvitationFromJson(Map<String, dynamic> json) =>
    _EventInvitation(
      id: json['id'] as String,
      invitationCode: json['invitationCode'] as String,
      eventId: (json['eventId'] as num).toInt(),
      eventName: json['eventName'] as String,
      ownerUserId: json['ownerUserId'] as String,
      ownerNickname: json['ownerNickname'] as String,
      invitedUserId: json['invitedUserId'] as String?,
      invitedNickname: json['invitedNickname'] as String?,
      status:
          $enumDecodeNullable(_$InvitationStatusEnumMap, json['status']) ??
          InvitationStatus.pending,
      createdAt: DateTime.parse(json['createdAt'] as String),
      expiresAt: DateTime.parse(json['expiresAt'] as String),
      respondedAt: json['respondedAt'] == null
          ? null
          : DateTime.parse(json['respondedAt'] as String),
    );

Map<String, dynamic> _$EventInvitationToJson(_EventInvitation instance) =>
    <String, dynamic>{
      'id': instance.id,
      'invitationCode': instance.invitationCode,
      'eventId': instance.eventId,
      'eventName': instance.eventName,
      'ownerUserId': instance.ownerUserId,
      'ownerNickname': instance.ownerNickname,
      'invitedUserId': instance.invitedUserId,
      'invitedNickname': instance.invitedNickname,
      'status': instance.status,
      'createdAt': instance.createdAt.toIso8601String(),
      'expiresAt': instance.expiresAt.toIso8601String(),
      'respondedAt': instance.respondedAt?.toIso8601String(),
    };

const _$InvitationStatusEnumMap = {
  InvitationStatus.pending: 'pending',
  InvitationStatus.accepted: 'accepted',
  InvitationStatus.rejected: 'rejected',
  InvitationStatus.expired: 'expired',
};
