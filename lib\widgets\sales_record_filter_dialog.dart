import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:syncfusion_flutter_datepicker/datepicker.dart';

import '../models/transaction_type.dart';
import '../providers/seller_provider.dart';
import '../providers/unified_workspace_provider.dart';
import '../utils/app_colors.dart';
import '../utils/dialog_theme.dart' as custom_dialog;
import 'modern_dialog_components.dart';

/// 판매기록 전용 필터 다이얼로그
/// 정렬 + 거래유형 + 판매자 + 기간을 포함
class SalesRecordFilterDialog extends ConsumerStatefulWidget {
  final String? initialSeller;
  final DateTimeRange? initialDateRange;
  final TransactionType? initialTransactionType;
  final String? initialSortOption;

  const SalesRecordFilterDialog({
    super.key,
    this.initialSeller,
    this.initialDateRange,
    this.initialTransactionType,
    this.initialSortOption,
  });

  @override
  ConsumerState<SalesRecordFilterDialog> createState() => _SalesRecordFilterDialogState();
}

class _SalesRecordFilterDialogState extends ConsumerState<SalesRecordFilterDialog> {
  late String _selectedSeller;
  late DateTimeRange? _selectedDateRange;
  late TransactionType? _selectedTransactionType;

  late DateRangePickerController _dateController;

  // 정렬 옵션들 (POS와 동일한 방식)
  static const List<Map<String, dynamic>> _sortOptions = [
    {'key': 'recent', 'title': '등록순', 'hasDirection': false},
    {'key': 'amount', 'title': '금액순', 'hasDirection': true},
  ];

  String _selectedSortKey = 'recent';
  bool _sortAscending = false;

  @override
  void initState() {
    super.initState();
    _selectedSeller = widget.initialSeller ?? '전체 판매자';
    _selectedDateRange = widget.initialDateRange;
    _selectedTransactionType = widget.initialTransactionType;

    // 기존 정렬 옵션을 새로운 방식으로 변환
    final initialSort = widget.initialSortOption ?? '최신순';
    if (initialSort.contains('금액')) {
      _selectedSortKey = 'amount';
      _sortAscending = initialSort.contains('낮은순');
    } else {
      _selectedSortKey = 'recent';
      _sortAscending = false;
    }

    _dateController = DateRangePickerController();

    if (_selectedDateRange != null) {
      _dateController.selectedRange = PickerDateRange(
        _selectedDateRange!.start,
        _selectedDateRange!.end,
      );
    }
  }

  @override
  void dispose() {
    _dateController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final sellerAsync = ref.watch(sellerNotifierProvider);
    final allSellersOption = '전체 판매자';
    final sellerNames = sellerAsync.isLoading
        ? <String>[]
        : sellerAsync.hasError
            ? <String>[]
            : sellerAsync.sellers.map((s) => s.name).toList()..sort();
    final sellers = [allSellersOption, ...sellerNames];
    final screenWidth = MediaQuery.of(context).size.width;
    final isTablet = screenWidth > 600;

    return custom_dialog.DialogTheme.buildResponsiveDialog(
      isCompact: true,
      child: Padding(
        padding: custom_dialog.DialogTheme.getCompactDialogPadding(isTablet),
        child: ConstrainedBox(
          constraints: BoxConstraints(
            maxHeight: custom_dialog.DialogTheme.getResponsiveMaxHeight(context),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
            // 제목
            Row(
              children: [
                Icon(
                  Icons.analytics,
                  size: isTablet ? 20 : 18,
                  color: AppColors.primarySeed,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    '판매기록 필터',
                    style: custom_dialog.DialogTheme.titleStyle.copyWith(
                      fontSize: isTablet ? 20.0 : 18.0,
                    ),
                  ),
                ),
              ],
            ),
            SizedBox(height: custom_dialog.DialogTheme.getCompactSectionSpacing(isTablet)),

            // 내용
            Container(
              constraints: BoxConstraints(
                maxHeight: custom_dialog.DialogTheme.getResponsiveMaxHeight(context),
              ),
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 정렬 방식 섹션
                    _buildSortSection(isTablet),
                    SizedBox(height: custom_dialog.DialogTheme.getCompactSectionSpacing(isTablet)),

                    // 거래 유형 선택 섹션
                    _buildTransactionTypeSection(isTablet),
                    SizedBox(height: custom_dialog.DialogTheme.getCompactSectionSpacing(isTablet)),

                    // 판매자 선택 섹션
                    _buildSellerSection(sellers, isTablet),
                    SizedBox(height: custom_dialog.DialogTheme.getCompactSectionSpacing(isTablet)),

                    // 기간 선택 섹션
                    _buildDateRangeSection(isTablet),
                  ],
                ),
              ),
            ),

            SizedBox(height: custom_dialog.DialogTheme.getCompactSectionSpacing(isTablet)),

            // 버튼
            Row(
              children: [
                Expanded(
                  child: custom_dialog.DialogTheme.buildModernButton(
                    text: '초기화',
                    onPressed: () {
                      setState(() {
                        _selectedSeller = allSellersOption;
                        _selectedDateRange = null;
                        _selectedTransactionType = null;
                        _selectedSortKey = 'recent';
                        _sortAscending = false;
                        _dateController.selectedRange = null;
                      });
                    },
                    isTablet: isTablet,
                    isPrimary: false,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: custom_dialog.DialogTheme.buildModernButton(
                    text: '적용',
                    onPressed: () {
                      Navigator.of(context).pop({
                        'seller': _selectedSeller,
                        'dateRange': _selectedDateRange,
                        'transactionType': _selectedTransactionType,
                        'sortOption': _getSortOptionString(),
                      });
                    },
                    isTablet: isTablet,
                    isPrimary: true,
                  ),
                ),
              ],
            ),
            ],
          ),
        ),
      ),
    );
  }

  /// 정렬 방식 섹션
  Widget _buildSortSection(bool isTablet) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '정렬 방식',
          style: TextStyle(
            fontSize: isTablet ? 16 : 14,
            fontWeight: FontWeight.w600,
            color: AppColors.onSurface,
            fontFamily: 'Pretendard',
          ),
        ),
        const SizedBox(height: 8),
        ..._sortOptions.map((option) => ModernDialogComponents.buildPOSSelectionTile(
          context: context,
          title: option['title'],
          subtitle: null, // 오름차순/내림차순 텍스트 제거
          trailing: option['hasDirection']
              ? Icon(
                  _selectedSortKey == option['key']
                      ? (_sortAscending ? Icons.keyboard_arrow_up : Icons.keyboard_arrow_down)
                      : Icons.keyboard_arrow_down, // 기본값으로 내림차순 화살표 표시
                  color: _selectedSortKey == option['key'] ? AppColors.primarySeed : AppColors.onSurfaceVariant,
                  size: isTablet ? 20 : 18,
                )
              : null,
          isSelected: _selectedSortKey == option['key'],
          isTablet: isTablet,
          onTap: () {
            setState(() {
              if (_selectedSortKey == option['key'] && option['hasDirection']) {
                _sortAscending = !_sortAscending;
              } else {
                _selectedSortKey = option['key'];
                _sortAscending = option['key'] == 'amount' ? false : true; // 금액은 기본 내림차순
              }
            });
          },
        )),
      ],
    );
  }

  /// 정렬 옵션을 문자열로 변환
  String _getSortOptionString() {
    switch (_selectedSortKey) {
      case 'amount':
        return _sortAscending ? '금액 낮은순' : '금액 높은순';
      case 'recent':
      default:
        return '최신순';
    }
  }

  /// 거래 유형 선택 섹션 (드롭다운으로 변경)
  Widget _buildTransactionTypeSection(bool isTablet) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '거래 유형',
          style: TextStyle(
            fontSize: isTablet ? 16 : 14,
            fontWeight: FontWeight.w600,
            color: AppColors.onSurface,
            fontFamily: 'Pretendard',
          ),
        ),
        const SizedBox(height: 8),
        DropdownButtonFormField<TransactionType?>(
          decoration: InputDecoration(
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: AppColors.onSurfaceVariant),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: AppColors.onSurfaceVariant),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: AppColors.primarySeed, width: 2),
            ),
            contentPadding: EdgeInsets.symmetric(
              horizontal: isTablet ? 16 : 12,
              vertical: isTablet ? 14 : 12,
            ),
          ),
          value: _selectedTransactionType,
          hint: Text(
            '거래 유형 선택',
            style: TextStyle(
              fontFamily: 'Pretendard',
              fontSize: isTablet ? 16 : 14,
              color: AppColors.onSurfaceVariant,
            ),
          ),
          items: [
            DropdownMenuItem<TransactionType?>(
              value: null,
              child: Text(
                '전체',
                style: TextStyle(
                  fontFamily: 'Pretendard',
                  fontSize: isTablet ? 16 : 14,
                  color: AppColors.onSurface,
                ),
              ),
            ),
            ...TransactionType.values.map((type) => DropdownMenuItem<TransactionType?>(
              value: type,
              child: Text(
                type.displayName,
                style: TextStyle(
                  fontFamily: 'Pretendard',
                  fontSize: isTablet ? 16 : 14,
                  color: AppColors.onSurface,
                ),
              ),
            )),
          ],
          onChanged: (value) {
            setState(() {
              _selectedTransactionType = value;
            });
          },
        ),
      ],
    );
  }

  /// 판매자 선택 섹션 (드롭다운)
  Widget _buildSellerSection(List<String> sellers, bool isTablet) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '판매자',
          style: TextStyle(
            fontSize: isTablet ? 16 : 14,
            fontWeight: FontWeight.w600,
            color: AppColors.onSurface,
            fontFamily: 'Pretendard',
          ),
        ),
        const SizedBox(height: 8),
        DropdownButtonFormField<String>(
          decoration: InputDecoration(
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: AppColors.onSurfaceVariant),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: AppColors.onSurfaceVariant),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: AppColors.primarySeed, width: 2),
            ),
            contentPadding: EdgeInsets.symmetric(
              horizontal: isTablet ? 16 : 12,
              vertical: isTablet ? 14 : 12,
            ),
            filled: true,
            fillColor: AppColors.surface,
          ),
          value: _selectedSeller,
          items: sellers.map((seller) => DropdownMenuItem(
            value: seller,
            child: Text(
              seller,
              style: TextStyle(
                fontFamily: 'Pretendard',
                fontSize: isTablet ? 16 : 14,
                color: AppColors.onSurface,
              ),
            ),
          )).toList(),
          onChanged: (value) {
            setState(() {
              _selectedSeller = value ?? '전체 판매자';
            });
          },
        ),
      ],
    );
  }

  /// 기간 선택 섹션
  Widget _buildDateRangeSection(bool isTablet) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Expanded(
              child: Text(
                '기간',
                style: TextStyle(
                  fontSize: isTablet ? 16 : 14,
                  fontWeight: FontWeight.w600,
                  color: AppColors.onSurface,
                  fontFamily: 'Pretendard',
                ),
              ),
            ),
            TextButton(
              onPressed: () {
                setState(() {
                  _selectedDateRange = null;
                  _dateController.selectedRange = null;
                });
              },
              child: Text(
                '전체 기간',
                style: TextStyle(
                  fontFamily: 'Pretendard',
                  color: AppColors.primarySeed,
                  fontWeight: FontWeight.w500,
                  fontSize: isTablet ? 14 : 12,
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        // 달력 버튼
        ModernDialogComponents.buildPOSSelectionTile(
          context: context,
          title: _selectedDateRange != null
              ? '${_selectedDateRange!.start.month}/${_selectedDateRange!.start.day} - ${_selectedDateRange!.end.month}/${_selectedDateRange!.end.day}'
              : '날짜 선택',
          subtitle: _selectedDateRange != null ? '선택된 기간' : '달력에서 기간을 선택하세요',
          isSelected: _selectedDateRange != null,
          isTablet: isTablet,
          trailing: Icon(
            Icons.calendar_today,
            color: _selectedDateRange != null ? AppColors.primarySeed : AppColors.onSurfaceVariant,
            size: isTablet ? 20 : 18,
          ),
          onTap: () => _showDateRangePicker(),
        ),
      ],
    );
  }

  /// 날짜 범위 선택 다이얼로그 표시
  void _showDateRangePicker() {
    final currentWorkspace = ref.read(currentWorkspaceProvider);
    final calendarSize = custom_dialog.DialogTheme.getCalendarDialogSize(context);

    // 행사 날짜 범위 설정 (행사가 없으면 기본값 사용)
    DateTime minDate = currentWorkspace?.startDate ?? DateTime.now().subtract(const Duration(days: 365));
    DateTime maxDate = currentWorkspace?.endDate ?? DateTime.now().add(const Duration(days: 365));

    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          backgroundColor: AppColors.surface,
          surfaceTintColor: Colors.transparent,
          title: Text(
            '기간 선택',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              color: AppColors.onSurface,
              fontWeight: FontWeight.w600,
            ),
          ),
          content: SizedBox(
            width: calendarSize.width,
            height: calendarSize.height,
            child: SfDateRangePicker(
              selectionMode: DateRangePickerSelectionMode.range,
              initialSelectedRange: _selectedDateRange != null
                  ? PickerDateRange(_selectedDateRange!.start, _selectedDateRange!.end)
                  : null,
              minDate: minDate,
              maxDate: maxDate,
              backgroundColor: AppColors.surface,
              todayHighlightColor: AppColors.primarySeed,
              selectionColor: AppColors.primarySeed,
              startRangeSelectionColor: AppColors.primarySeed,
              endRangeSelectionColor: AppColors.primarySeed,
              rangeSelectionColor: AppColors.primarySeed.withValues(alpha: 0.3),
              selectionTextStyle: TextStyle(
                color: AppColors.onPrimary,
                fontWeight: FontWeight.w500,
                fontFamily: 'Pretendard',
              ),
              rangeTextStyle: TextStyle(
                color: AppColors.onSurface,
                fontWeight: FontWeight.w400,
                fontFamily: 'Pretendard',
              ),
              headerStyle: DateRangePickerHeaderStyle(
                backgroundColor: AppColors.surface,
                textStyle: TextStyle(
                  color: AppColors.onSurface,
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  fontFamily: 'Pretendard',
                ),
              ),
              monthViewSettings: DateRangePickerMonthViewSettings(
                firstDayOfWeek: 1, // 월요일부터 시작
                dayFormat: 'EEE',
                viewHeaderStyle: DateRangePickerViewHeaderStyle(
                  backgroundColor: AppColors.surface,
                  textStyle: TextStyle(
                    color: AppColors.onSurfaceVariant,
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                    fontFamily: 'Pretendard',
                  ),
                ),
              ),
              monthCellStyle: DateRangePickerMonthCellStyle(
                textStyle: TextStyle(
                  fontFamily: 'Pretendard',
                  color: AppColors.onSurface,
                ),
              ),
              onSelectionChanged: (DateRangePickerSelectionChangedArgs args) {
                if (args.value is PickerDateRange) {
                  final range = args.value as PickerDateRange;
                  if (range.startDate != null) {
                    // endDate가 null이면 startDate와 같은 날짜로 설정 (단일 날짜 선택)
                    final endDate = range.endDate ?? range.startDate!;
                    setState(() {
                      _selectedDateRange = DateTimeRange(start: range.startDate!, end: endDate);
                      _dateController.selectedRange = PickerDateRange(range.startDate!, endDate);
                    });
                  }
                }
              },
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(
                '취소',
                style: TextStyle(color: AppColors.onSurfaceVariant),
              ),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primarySeed,
                foregroundColor: AppColors.onPrimary,
              ),
              child: const Text('확인'),
            ),
          ],
        );
      },
    );
  }
}
