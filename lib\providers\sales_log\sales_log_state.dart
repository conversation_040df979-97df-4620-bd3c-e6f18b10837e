import '../../models/sales_log.dart';
import '../../models/sales_log_display_item.dart';
import '../../models/sales_stat_item.dart';
import '../../models/transaction_type.dart';
import '../base_state.dart';

/// 판매 기록(영수증/로그) 상태를 관리하는 State 클래스입니다.
/// - 전체 판매 기록, 필터링/정렬 결과, 검색어, 로딩/에러/업데이트 상태 등 포함
/// - ProviderException, isCancelled 등 오류/취소/비동기 상태도 함께 관리
class SalesLogState extends BaseState {
  final List<SalesLog> salesLogs; // 현재 페이지에 표시되는 데이터
  final List<SalesLog> allSalesLogs; // 전체 데이터 (클라이언트 사이드 페이지네이션용)
  final List<SalesLog> filteredSalesLogs;
  final List<SalesLogDisplayItem> displayItems;
  final List<String> sellerNames;
  final List<SalesStatItem> salesStats;
  final String selectedSellerFilter;
  final TransactionType? selectedTypeFilter;
  final bool isUpdating;
  final double? batchProgress;
  final String? batchOperation;
  final bool isBatchProcessing;
  // 페이지네이션 관련 필드
  final int currentPage;
  final int pageSize;
  final int totalCount;
  final bool hasMoreData;
  final bool isLoadingMore;

  const SalesLogState({
    this.salesLogs = const [],
    this.allSalesLogs = const [],
    this.filteredSalesLogs = const [],
    this.displayItems = const [],
    this.sellerNames = const [],
    this.salesStats = const [],
    this.selectedSellerFilter = '',
    this.selectedTypeFilter,
    this.isUpdating = false,
    this.batchProgress,
    this.batchOperation,
    this.isBatchProcessing = false,
    this.currentPage = 1,
    this.pageSize = 50,
    this.totalCount = 0,
    this.hasMoreData = false,
    this.isLoadingMore = false,
    bool isLoading = false,
    String? errorMessage,
    String? errorCode,
    String? errorSeverity,
    Map<String, String>? errorDetails,
    bool isCancelled = false,
  }) : super(
    isLoading: isLoading,
    errorMessage: errorMessage,
    errorCode: errorCode,
    errorSeverity: errorSeverity,
    errorDetails: errorDetails,
    isCancelled: isCancelled,
  );

  SalesLogState copyWithBase({
    bool? isLoading,
    String? errorMessage,
    String? errorCode,
    String? errorSeverity,
    Map<String, String>? errorDetails,
    bool? isCancelled,
  }) {
    return copyWith(
      isLoading: isLoading,
      errorMessage: errorMessage,
      errorCode: errorCode,
      errorSeverity: errorSeverity,
      errorDetails: errorDetails,
      isCancelled: isCancelled,
    );
  }

  SalesLogState copyWith({
    List<SalesLog>? salesLogs,
    List<SalesLog>? allSalesLogs,
    List<SalesLog>? filteredSalesLogs,
    List<SalesLogDisplayItem>? displayItems,
    List<String>? sellerNames,
    List<SalesStatItem>? salesStats,
    String? selectedSellerFilter,
    TransactionType? selectedTypeFilter,
    bool? isUpdating,
    double? batchProgress,
    String? batchOperation,
    bool? isBatchProcessing,
    int? currentPage,
    int? pageSize,
    int? totalCount,
    bool? hasMoreData,
    bool? isLoadingMore,
    bool? isLoading,
    String? errorMessage,
    String? errorCode,
    String? errorSeverity,
    Map<String, String>? errorDetails,
    bool? isCancelled,
  }) {
    return SalesLogState(
      salesLogs: salesLogs ?? this.salesLogs,
      allSalesLogs: allSalesLogs ?? this.allSalesLogs,
      filteredSalesLogs: filteredSalesLogs ?? this.filteredSalesLogs,
      displayItems: displayItems ?? this.displayItems,
      sellerNames: sellerNames ?? this.sellerNames,
      salesStats: salesStats ?? this.salesStats,
      selectedSellerFilter: selectedSellerFilter ?? this.selectedSellerFilter,
      selectedTypeFilter: selectedTypeFilter ?? this.selectedTypeFilter,
      isUpdating: isUpdating ?? this.isUpdating,
      batchProgress: batchProgress ?? this.batchProgress,
      batchOperation: batchOperation ?? this.batchOperation,
      isBatchProcessing: isBatchProcessing ?? this.isBatchProcessing,
      currentPage: currentPage ?? this.currentPage,
      pageSize: pageSize ?? this.pageSize,
      totalCount: totalCount ?? this.totalCount,
      hasMoreData: hasMoreData ?? this.hasMoreData,
      isLoadingMore: isLoadingMore ?? this.isLoadingMore,
      isLoading: isLoading ?? this.isLoading,
      errorMessage: errorMessage ?? this.errorMessage,
      errorCode: errorCode ?? this.errorCode,
      errorSeverity: errorSeverity ?? this.errorSeverity,
      errorDetails: errorDetails ?? this.errorDetails,
      isCancelled: isCancelled ?? this.isCancelled,
    );
  }

  @override
  List<Object?> get props => [
    ...super.props,
    salesLogs,
    filteredSalesLogs,
    displayItems,
    sellerNames,
    salesStats,
    selectedSellerFilter,
    selectedTypeFilter,
    isUpdating,
    batchProgress,
    batchOperation,
    isBatchProcessing,
    currentPage,
    pageSize,
    totalCount,
    hasMoreData,
    isLoadingMore,
  ];
} 
