/// 바라 부스 매니저 - 행사 권한 데이터 모델
///
/// 행사에서의 사용자 권한 정보를 표현하는 데이터 모델 클래스입니다.
/// - 사용자 역할, 권한 목록, 행사 정보 등 포함
/// - Firebase Firestore 연동 지원
/// - freezed를 사용하여 불변 객체로 생성
///
/// 작성자: Blue
/// 버전: 1.0.0
/// 최종 업데이트: 2025년 8월
library;

import 'package:freezed_annotation/freezed_annotation.dart';
import 'user_role.dart';
import 'permission.dart';

part 'event_permission.freezed.dart';
part 'event_permission.g.dart';

/// 행사 권한 정보를 담는 모델 클래스입니다.
/// freezed를 사용하여 불변 객체로 생성
@freezed
abstract class EventPermission with _$EventPermission {
  const factory EventPermission({
    /// 행사 ID
    required int eventId,
    
    /// 사용자 ID
    required String userId,
    
    /// 사용자 닉네임 (캐시용)
    required String userNickname,
    
    /// 사용자 역할
    required UserRole role,
    
    /// 권한 목록
    @Default([]) List<Permission> permissions,
    
    /// 권한 부여 시간
    required DateTime grantedAt,
    
    /// 권한 수정 시간
    DateTime? updatedAt,
    
    /// 권한을 부여한 사용자 ID (소유자)
    String? grantedByUserId,
  }) = _EventPermission;

  factory EventPermission.fromJson(Map<String, dynamic> json) => _$EventPermissionFromJson(json);

  /// Firebase 문서에서 안전하게 생성
  factory EventPermission.fromFirebaseMap(Map<String, dynamic> map) {
    try {
      final permissionStrings = (map['permissions'] as List<dynamic>?)?.cast<String>() ?? [];
      final permissions = permissionStrings.map((p) => Permission.fromString(p)).toList();
      
      return EventPermission(
        eventId: map['eventId'] as int,
        userId: map['userId'] as String,
        userNickname: map['userNickname'] as String,
        role: UserRole.fromString(map['role'] as String),
        permissions: permissions,
        grantedAt: DateTime.parse(map['grantedAt'] as String),
        updatedAt: map['updatedAt'] != null ? DateTime.parse(map['updatedAt'] as String) : null,
        grantedByUserId: map['grantedByUserId'] as String?,
      );
    } catch (e) {
      throw FormatException('Firebase EventPermission 데이터 파싱 실패: $e\nData: $map');
    }
  }

  /// 소유자 권한 생성을 위한 팩토리 생성자
  factory EventPermission.createOwner({
    required int eventId,
    required String userId,
    required String userNickname,
  }) {
    return EventPermission(
      eventId: eventId,
      userId: userId,
      userNickname: userNickname,
      role: UserRole.owner,
      permissions: Permission.allPermissions,
      grantedAt: DateTime.now(),
    );
  }

  /// 초대받은 사용자 권한 생성을 위한 팩토리 생성자
  factory EventPermission.createInvited({
    required int eventId,
    required String userId,
    required String userNickname,
    required String grantedByUserId,
    List<Permission>? customPermissions,
  }) {
    return EventPermission(
      eventId: eventId,
      userId: userId,
      userNickname: userNickname,
      role: UserRole.invited,
      permissions: customPermissions ?? Permission.defaultInvitedPermissions,
      grantedAt: DateTime.now(),
      grantedByUserId: grantedByUserId,
    );
  }
}

/// EventPermission 확장 메서드
extension EventPermissionExtension on EventPermission {
  /// Firebase 업로드용 맵 변환
  Map<String, dynamic> toFirebaseMap() {
    final map = <String, dynamic>{
      'eventId': eventId,
      'userId': userId,
      'userNickname': userNickname,
      'role': role.value,
      'permissions': permissions.map((p) => p.value).toList(),
      'grantedAt': grantedAt.toIso8601String(),
    };

    // 선택적 필드들 (null이 아닌 경우만 포함)
    if (updatedAt != null) {
      map['updatedAt'] = updatedAt!.toIso8601String();
    }
    if (grantedByUserId != null) {
      map['grantedByUserId'] = grantedByUserId;
    }

    return map;
  }

  /// 특정 권한을 가지고 있는지 확인
  bool hasPermission(Permission permission) {
    return permissions.contains(permission);
  }

  /// 읽기 권한 확인
  bool get canRead => hasPermission(Permission.read);

  /// 다운로드 권한 확인
  bool get canDownload => hasPermission(Permission.download);

  /// 선입금 관리 권한 확인
  bool get canManagePrepayment => hasPermission(Permission.prepaymentManage);

  /// 소유자 권한 확인
  bool get isOwner => role.isOwner;

  /// 초대받은 사용자 권한 확인
  bool get isInvited => role.isInvited;

  /// 모든 권한을 가지고 있는지 확인
  bool get hasAllPermissions => permissions.length == Permission.allPermissions.length;
}
