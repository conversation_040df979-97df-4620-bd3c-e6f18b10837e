import 'dart:typed_data';
import 'package:excel/excel.dart';
import 'dart:convert';
import '../models/prepayment.dart';
import '../models/product.dart';
import '../models/prepayment_virtual_product.dart';
import 'logger_utils.dart';

/// 엑셀 파일 처리 유틸리티 클래스입니다.
/// 위치폼에서 추출한 선입금 데이터를 파싱하고 처리합니다.
class ExcelProcessor {
  static const String _tag = 'ExcelProcessor';
  
  /// 열 매핑 정보를 저장하는 맵
  late Map<String, int> _columnMapping;
  
  /// 헤더 행을 분석하여 열 매핑을 생성합니다.
  void analyzeHeader(List<Data?> headerRow) {
    _columnMapping = {};
    
    LoggerUtils.logInfo('헤더 분석 시작: ${headerRow.length}개 열', tag: _tag);
    
    for (int i = 0; i < headerRow.length; i++) {
      final cellValue = headerRow[i]?.value?.toString().trim() ?? '';
      if (cellValue.isNotEmpty) {
        _columnMapping[cellValue] = i;
        LoggerUtils.logInfo('열 $i: "$cellValue"', tag: _tag);
      }
    }
    
    LoggerUtils.logInfo('헤더 분석 완료: ${_columnMapping.length}개 열 매핑됨', tag: _tag);
    LoggerUtils.logInfo('매핑된 열들: $_columnMapping', tag: _tag);
  }
  
  /// 엑셀 파일에서 선입금 데이터를 추출합니다.
  /// 
  /// [bytes]: 엑셀 파일의 바이트 데이터
  /// [settings]: 엑셀 설정 정보
  /// 
  /// 반환값: 추출된 선입금 데이터 리스트
  static Future<List<ExcelPrepaymentData>> processPrepaymentExcel(
    Uint8List bytes, {
    required bool collectDayOfWeek,
    required int dayOfWeekColumnIndex,
    String formType = 'slim',
  }) async {
    LoggerUtils.methodStart('processPrepaymentExcel', tag: _tag);

    try {
      final excel = Excel.decodeBytes(bytes);
      final sheet = excel.tables.values.first;
      
      if (sheet.rows.isEmpty) {
        LoggerUtils.logWarning('엑셀 파일이 비어있습니다', tag: _tag);
        return [];
      }

      final processor = ExcelProcessor();
      
      // 헤더 행 분석
      processor.analyzeHeader(sheet.rows[0]);
      
      // 기준점 찾기
      final timeColumnIndex = processor._findTimeColumn();
      final deliveryColumnIndex = processor._findDeliveryColumn();
      
      if (timeColumnIndex == -1 || deliveryColumnIndex == -1) {
        throw Exception('작성시간 또는 배송방법 열을 찾을 수 없습니다.');
      }
      
      LoggerUtils.logInfo(
        '기준점 찾기 완료: 작성시간(${timeColumnIndex}), 배송방법(${deliveryColumnIndex})',
        tag: _tag,
      );

      // 데이터 행 처리
      final List<ExcelPrepaymentData> results = [];

      for (int i = 1; i < sheet.rows.length; i++) {
        final dataRow = sheet.rows[i];
        final prepaymentData = processor._processPrepaymentDataRow(
          dataRow,
          timeColumnIndex,
          deliveryColumnIndex,
          collectDayOfWeek,
          dayOfWeekColumnIndex,
          i,
          formType,
        );

        if (prepaymentData != null) {
          results.add(prepaymentData);
        }
      }

      LoggerUtils.logInfo('총 ${results.length}개의 선입금 데이터 추출 완료', tag: _tag);
      return results;
    } catch (e, stackTrace) {
      LoggerUtils.logError(
        '선입금 엑셀 처리 중 오류 발생',
        tag: _tag,
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }



  /// 작성시간 열을 찾습니다.
  int _findTimeColumn() {
    final timeColumn = _columnMapping.entries
        .where((e) => e.key.contains('작성시간') || e.key.contains('시간'))
        .map((e) => e.value)
        .firstOrNull ?? -1;
    
    LoggerUtils.logInfo('작성시간 열 찾기: $timeColumn', tag: _tag);
    return timeColumn;
  }

  /// 배송방법 열을 찾습니다.
  int _findDeliveryColumn() {
    final deliveryColumn = _columnMapping.entries
        .where((e) => e.key.contains('배송방법') || e.key.contains('배송'))
        .map((e) => e.value)
        .firstOrNull ?? -1;
    
    LoggerUtils.logInfo('배송방법 열 찾기: $deliveryColumn', tag: _tag);
    return deliveryColumn;
  }

  /// 선입금 데이터 행을 처리합니다.
  ExcelPrepaymentData? _processPrepaymentDataRow(
    List<Data?> dataRow,
    int timeColumnIndex,
    int deliveryColumnIndex,
    bool collectDayOfWeek,
    int dayOfWeekColumnIndex,
    int rowIndex,
    String formType,
  ) {
    try {
      // 기본 정보 추출
      final orderNumber = _getCellValue(dataRow, 0); // A열: 번호
      
      // 작성시간 추출
      final writtenTimeString = _getCellValue(dataRow, timeColumnIndex);
      DateTime? writtenTime;
      try {
        // 작성시간 파싱 (예: "2024-01-15 14:30:00" 형태)
        if (writtenTimeString.isNotEmpty) {
          writtenTime = DateTime.parse(writtenTimeString);
        }
      } catch (e) {
        LoggerUtils.logWarning(
          '작성시간 파싱 실패 (행 ${rowIndex + 1}): $writtenTimeString',
          tag: _tag,
        );
      }
      
      // 상품 데이터 추출 (작성시간 다음 열부터 배송방법 이전 열까지)
      final Map<PrepaymentVirtualProduct, int> purchasedProducts = {};
      LoggerUtils.logInfo('상품 데이터 추출 시작: 작성시간($timeColumnIndex) ~ 배송방법($deliveryColumnIndex)', tag: _tag);
      LoggerUtils.logInfo('전체 헤더 매핑: $_columnMapping', tag: _tag);
      
      for (int i = timeColumnIndex + 1; i < deliveryColumnIndex; i++) {
        final productName = _getHeaderName(i);
        final quantityValue = _getCellValue(dataRow, i);
        final quantity = int.tryParse(quantityValue) ?? 0;
        
        LoggerUtils.logInfo('열 $i 분석:', tag: _tag);
        LoggerUtils.logInfo('  - 헤더명: "$productName"', tag: _tag);
        LoggerUtils.logInfo('  - 셀값: "$quantityValue"', tag: _tag);
        LoggerUtils.logInfo('  - 파싱된수량: $quantity', tag: _tag);
        LoggerUtils.logInfo('  - 상품명비어있음: ${productName.isEmpty}', tag: _tag);
        LoggerUtils.logInfo('  - 수량0이하: ${quantity <= 0}', tag: _tag);
        
        if (quantity > 0 && productName.isNotEmpty) {
          final cleanProductName = _cleanProductName(productName);
          
          // 가상 상품 객체 생성 (임시 ID는 0으로 설정, 나중에 실제 ID로 교체)
          // 주의: eventId는 실제 사용 시 현재 행사 ID로 교체되어야 합니다.
          final virtualProduct = PrepaymentVirtualProduct(
            id: 0, // 임시 ID
            name: cleanProductName,
            price: 0.0, // 가격은 나중에 설정
            quantity: 0, // 초기 수량은 0
            createdAt: DateTime.now(),
            eventId: 0, // 임시값, 실제 사용 시 현재 행사 ID로 교체 필요
          );
          
          purchasedProducts[virtualProduct] = quantity;
          LoggerUtils.logInfo('✅ 가상 상품 추가됨: $cleanProductName ($quantity개)', tag: _tag);
        } else {
          LoggerUtils.logInfo('❌ 상품 제외됨: 상품명="${productName}", 수량=$quantity', tag: _tag);
        }
      }
      
      LoggerUtils.logInfo('구매 상품 파싱 완료: ${purchasedProducts.length}개 상품', tag: _tag);
      LoggerUtils.logInfo('구매 상품 목록: ${purchasedProducts.entries.map((e) => '${e.key.name}(${e.value}개)').join(', ')}', tag: _tag);
      
      // 개인정보 추출 (이메일~트위터계정 사이 열 동적 계산)
      final emailIdx = _columnMapping.entries
          .firstWhere(
            (e) => e.key.contains('이메일') || e.key.toLowerCase().contains('email'),
            orElse: () => const MapEntry('', -1),
          )
          .value;
      final twitterIdx = _columnMapping.entries
          .firstWhere(
            (e) => e.key.contains('트위터') || e.key.toLowerCase().contains('twitter'),
            orElse: () => const MapEntry('', -1),
          )
          .value;
      
      // 이메일과 트위터 계정 사이의 열들을 메모로 수집
      final List<String> memoParts = [];
      String? dayOfWeek;
      
      if (emailIdx != -1 && twitterIdx != -1 && twitterIdx > emailIdx) {
        // 이메일과 트위터 계정 사이의 열들을 처리
        for (int i = emailIdx + 1; i < twitterIdx; i++) {
          final header = _getHeaderName(i);
          final value = _getCellValue(dataRow, i);
          
          if (value.isNotEmpty) {
            // 요일 수집이 활성화되고 해당 열이 요일 열인 경우
            if (collectDayOfWeek && i == dayOfWeekColumnIndex) {
              dayOfWeek = _extractDayOfWeek(value);
              // 요일 데이터도 메모에 포함
              memoParts.add('$header: $value');
            } else {
              // 요일 열이 아니거나 요일 수집이 비활성화된 경우 메모에 추가
              memoParts.add('$header: $value');
            }
          }
        }
      } else {
        // 이메일이나 트위터 계정을 찾을 수 없는 경우 배송방법 이후 열들을 처리
        for (int i = deliveryColumnIndex + 1; i < dataRow.length; i++) {
          final header = _getHeaderName(i);
          final value = _getCellValue(dataRow, i);
          
          if (value.isNotEmpty) {
            // 요일 수집이 활성화되고 해당 열이 요일 열인 경우
            if (collectDayOfWeek && i == dayOfWeekColumnIndex) {
              dayOfWeek = _extractDayOfWeek(value);
              // 요일 데이터도 메모에 포함
              memoParts.add('$header: $value');
            } else {
              // 요일 열이 아니거나 요일 수집이 비활성화된 경우 메모에 추가
              memoParts.add('$header: $value');
            }
          }
        }
      }
      
      final memo = memoParts.isNotEmpty ? memoParts.join('\n') : '';
      
      // 페이폼인 경우 진행상태 확인
      if (formType == 'payform') {
        final status = _findPersonalInfoValue(dataRow, deliveryColumnIndex, ['진행상태', '상태']);
        if (status == '자동주문취소' || status == '주문취소') {
          LoggerUtils.logInfo('주문 취소된 행 제외 (행 ${rowIndex + 1}): $status', tag: _tag);
          return null;
        }
      }

      // 개인정보/금액 등
      final contact = _findPersonalInfoValue(dataRow, deliveryColumnIndex, ['주문자연락처', '연락처', '전화번호']);
      final email = _findPersonalInfoValue(dataRow, deliveryColumnIndex, ['이메일', 'email']);
      final bankName = _findPersonalInfoValue(dataRow, deliveryColumnIndex, ['환불은행', '은행', '계좌']);

      // 폼 타입에 따라 구매자명 파싱 방식 변경
      final buyerName = formType == 'payform'
          ? _findPersonalInfoValue(dataRow, deliveryColumnIndex, ['주문자명', '주문자', '구매자명'])
          : _findPersonalInfoValue(dataRow, deliveryColumnIndex, ['환불예금주', '예금주', '구매자']);

      final twitterAccount = _findPersonalInfoValue(dataRow, deliveryColumnIndex, ['트위터계정', '트위터', 'twitter']);
      final totalAmountValue = _findPersonalInfoValue(dataRow, deliveryColumnIndex, ['총 주문금액', '주문금액', '총액']);
      final totalAmount = int.tryParse(totalAmountValue) ?? 0;
      
      if (orderNumber.isEmpty) {
        LoggerUtils.logWarning(
          '필수 정보 누락 (행 ${rowIndex + 1}): 주문번호=$orderNumber',
          tag: _tag,
        );
        return null;
      }
      
      return ExcelPrepaymentData(
        orderNumber: orderNumber,
        contact: contact,
        email: email,
        bankName: bankName,
        buyerName: buyerName, // 환불예금주(예금주, 구매자 등)로 저장
        twitterAccount: twitterAccount.isNotEmpty ? twitterAccount : null,
        memo: memo.isNotEmpty ? memo : null,
        dayOfWeek: dayOfWeek,
        writtenTime: writtenTime, // 작성시간 추가
        purchasedProducts: purchasedProducts,
        totalAmount: totalAmount,
        rowIndex: rowIndex,
      );
    } catch (e, stackTrace) {
      LoggerUtils.logError(
        '선입금 데이터 행 처리 중 오류 (행 ${rowIndex + 1})',
        tag: _tag,
        error: e,
        stackTrace: stackTrace,
      );
      return null;
    }
  }



  /// 셀 값을 안전하게 추출합니다.
  String _getCellValue(List<Data?> row, int index) {
    if (index < 0 || index >= row.length) return '';
    return row[index]?.value?.toString().trim() ?? '';
  }

  /// 헤더 이름을 가져옵니다.
  String _getHeaderName(int columnIndex) {
    return _columnMapping.entries
        .where((e) => e.value == columnIndex)
        .map((e) => e.key)
        .firstOrNull ?? '';
  }

  /// 상품명에서 [상품숫자] 패턴을 제거합니다.
  String _cleanProductName(String productName) {
    // [상품숫자] 패턴 제거 (예: [상품1], [상품2] 등)
    final cleanName = productName.replaceAll(RegExp(r'\[상품\d+\]'), '').trim();
    return cleanName;
  }

  /// 개인정보 값을 키워드로 찾아서 반환합니다.
  String _findPersonalInfoValue(List<Data?> dataRow, int deliveryColumnIndex, List<String> keywords) {
    // 배송방법 열 이후부터 끝까지 검색
    for (int i = deliveryColumnIndex + 1; i < dataRow.length; i++) {
      final headerName = _getHeaderName(i);
      final cellValue = _getCellValue(dataRow, i);
      
      // 헤더 이름이 키워드와 일치하는지 확인
      for (final keyword in keywords) {
        if (headerName.toLowerCase().contains(keyword.toLowerCase())) {
          return cellValue;
        }
      }
    }
    return '';
  }

  /// 엑셀 데이터에서 상품 정보를 추출합니다.
  /// 상품 종류별로 하나씩만 추출하여 중복 없는 상품 목록을 만듭니다.
  static List<ExcelProductData> extractProducts(List<ExcelPrepaymentData> prepaymentData) {
    final Map<String, int> productQuantities = {};
    
    LoggerUtils.logInfo('상품 데이터 추출 시작: ${prepaymentData.length}개 선입금 데이터', tag: 'ExcelProcessor');
    
    for (final data in prepaymentData) {
      LoggerUtils.logInfo('선입금 데이터 처리: ${data.orderNumber}, 상품 수: ${data.purchasedProducts.length}', tag: 'ExcelProcessor');
      
      for (final entry in data.purchasedProducts.entries) {
        final productName = entry.key.name; // 가상 상품의 이름 사용
        final quantity = entry.value;
        
        LoggerUtils.logInfo('상품 처리: $productName (수량: $quantity)', tag: 'ExcelProcessor');
        
        productQuantities[productName] = (productQuantities[productName] ?? 0) + quantity;
        
        LoggerUtils.logInfo('상품 수량 누적: $productName (총 수량: ${productQuantities[productName]})', tag: 'ExcelProcessor');
      }
    }
    
    final result = productQuantities.entries.map((entry) {
      return ExcelProductData(
        name: entry.key,
        totalQuantity: entry.value,
        price: 0, // 나중에 설정에서 입력받음
      );
    }).toList();
    
    LoggerUtils.logInfo('상품 데이터 추출 완료: ${result.length}개 고유 상품', tag: 'ExcelProcessor');
    LoggerUtils.logInfo('추출된 상품 목록: ${result.map((p) => '${p.name}(${p.totalQuantity}개)').join(', ')}', tag: 'ExcelProcessor');
    
    return result;
  }

  /// 요일 문자열에서 요일 번호를 추출합니다.
  String? _extractDayOfWeek(String value) {
    final cleanValue = value.trim().toLowerCase();
    final List<String> foundDays = [];
    
    // 요일 패턴 매칭 - 정확히 "N요일" 패턴만 인식
    if (cleanValue.contains('월요일')) {
      foundDays.add('월요일');
    }
    if (cleanValue.contains('화요일')) {
      foundDays.add('화요일');
    }
    if (cleanValue.contains('수요일')) {
      foundDays.add('수요일');
    }
    if (cleanValue.contains('목요일')) {
      foundDays.add('목요일');
    }
    if (cleanValue.contains('금요일')) {
      foundDays.add('금요일');
    }
    if (cleanValue.contains('토요일')) {
      foundDays.add('토요일');
    }
    if (cleanValue.contains('일요일')) {
      foundDays.add('일요일');
    }
    
    // 찾은 요일들을 쉼표로 구분하여 반환
    if (foundDays.isNotEmpty) {
      return foundDays.join(', ');
    }
    
    // 요일이 발견되지 않으면 '없음' 반환
    return '없음';
  }

  static ExcelPrepaymentData fromPrepayment(Prepayment prepayment) {
    // Prepayment의 필드들을 ExcelPrepaymentData에 맞게 복사
    return ExcelPrepaymentData(
      orderNumber: prepayment.orderNumber ?? prepayment.id.toString(), // orderNumber가 있으면 사용, 없으면 id 사용
      contact: prepayment.buyerContact,
      email: prepayment.email,
      bankName: prepayment.bankName,
      buyerName: prepayment.buyerName,
      twitterAccount: prepayment.twitterAccount,
      memo: prepayment.memo,
      dayOfWeek: prepayment.pickupDays.isNotEmpty ? prepayment.pickupDays.join(', ') : null,
      writtenTime: prepayment.registrationDate,
      purchasedProducts: {
        for (final p in prepayment.purchasedProducts)
          PrepaymentVirtualProduct(
            id: 0, // DB에서 id를 모를 때는 0으로
            name: p.name,
            price: p.price,
            quantity: p.quantity,
            createdAt: prepayment.registrationDate,
            eventId: prepayment.eventId, // Prepayment의 eventId 사용
          ): p.quantity
      },
      totalAmount: prepayment.amount,
      rowIndex: 0, // DB 데이터이므로 rowIndex는 0 또는 -1로 설정
    );
  }
}

/// 엑셀에서 추출한 선입금 데이터를 표현하는 클래스입니다.
class ExcelPrepaymentData {
  final String orderNumber;
  final String contact;
  final String email;
  final String bankName;
  final String buyerName;
  final String? twitterAccount;
  final String? memo;
  final String? dayOfWeek;
  final DateTime? writtenTime; // 작성시간 추가
  final Map<PrepaymentVirtualProduct, int> purchasedProducts; // 가상 상품으로 변경
  final int totalAmount;
  final int rowIndex;

  const ExcelPrepaymentData({
    required this.orderNumber,
    required this.contact,
    required this.email,
    required this.bankName,
    required this.buyerName,
    this.twitterAccount,
    this.memo,
    this.dayOfWeek,
    this.writtenTime, // 작성시간 추가
    required this.purchasedProducts,
    required this.totalAmount,
    required this.rowIndex,
  });

  Prepayment toPrepayment({
    required int id,
    required int amount,
    required String productNameList,
  }) {
    // registrationDate를 초 단위로 변환
    final rawDate = writtenTime ?? DateTime.now();
    final registrationDate = DateTime(
      rawDate.year, rawDate.month, rawDate.day,
      rawDate.hour, rawDate.minute, rawDate.second
    );
    // 요일 파싱
    List<String> days = dayOfWeek != null
      ? dayOfWeek!.split(',').map((e) => e.trim()).toList()
      : [Prepayment.noDayOfWeek];
    int registrationActualDayOfWeek = 8;
    if (days.length == 1 && days.first == Prepayment.noDayOfWeek) {
      registrationActualDayOfWeek = 8;
    } else if (days.contains(Prepayment.noDayOfWeek)) {
      registrationActualDayOfWeek = 8;
    } else if (days.isNotEmpty) {
      final idx = Prepayment.availableDaysOfWeek.indexOf(days.first);
      if (idx >= 0 && idx < 7) {
        registrationActualDayOfWeek = idx + 1;
      }
    }
    // purchasedProductsJson 생성 (정확한 수량 반영)
    final purchasedProductsList = purchasedProducts.entries.map((e) => {
      'name': e.key.name,
      'quantity': e.value,
      'price': e.key.price,
    }).toList();
    final purchasedProductsJson = jsonEncode(purchasedProductsList);
    return Prepayment(
      id: id,
      buyerName: buyerName,
      buyerContact: contact,
      amount: amount,
      pickupDays: days,
      productNameList: productNameList,
      purchasedProductsJson: purchasedProductsJson,
      memo: memo,
      registrationDate: registrationDate,
      isReceived: false,
      registrationActualDayOfWeek: registrationActualDayOfWeek,
      bankName: bankName,
      email: email,
      twitterAccount: twitterAccount,
      registrationTimestamp: registrationDate.millisecondsSinceEpoch,
      orderNumber: orderNumber, // 주문번호 추가
    );
  }
}

/// 엑셀에서 추출한 상품 데이터를 표현하는 클래스입니다.
class ExcelProductData {
  final String name;
  final int totalQuantity;
  final int price;

  const ExcelProductData({
    required this.name,
    required this.totalQuantity,
    required this.price,
  });

  /// Product 모델로 변환합니다.
  Product toProduct({
    required int id,
    required String? imagePath,
    String? sellerName,
    required int eventId, // 현재 행사 ID 필수로 추가
  }) {
    return Product(
      id: id,
      name: name,
      quantity: totalQuantity,
      price: price,
      imagePath: imagePath,
      sellerName: sellerName,
      lastServicedDate: null,
      isActive: true,

      eventId: eventId, // 현재 행사 ID 설정
    );
  }
} 
