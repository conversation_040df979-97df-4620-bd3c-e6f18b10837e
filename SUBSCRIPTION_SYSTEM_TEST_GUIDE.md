# 구독 시스템 테스트 가이드

## 🎯 테스트 목적
바라 부스 매니저 앱의 구독 시스템이 정상적으로 작동하는지 확인합니다.
- 결제 후 구독 상태 전환
- 프로 기능 활성화
- 정기결제 처리
- 구독 취소 플로우

## 📋 테스트 체크리스트

### 1. 카드 등록 및 빌링키 발급
- [ ] 앱 실행 → 설정 → 구독 관리 접근
- [ ] "프로 플랜 구독하기" 버튼 클릭
- [ ] 카드 정보 입력 (테스트 카드 사용)
- [ ] 빌링키 발급 성공 메시지 확인
- [ ] Firestore `users/{uid}/cards` 컬렉션에 카드 정보 저장 확인

### 2. 구독 시작 및 첫 결제
- [ ] `NicePaySubscriptionService.startSubscription()` 호출
- [ ] 첫 결제 4900원 처리 성공 확인
- [ ] Firestore `users/{uid}/subscription` 문서 상태 확인:
  - `status: 'active'`
  - `plan: 'pro'`
  - `price: 4900`
  - `startedAt: 현재시간`
  - `nextPaymentDate: 30일 후`

### 3. 프로 기능 활성화 확인
- [ ] 앱 재시작 후 구독 상태 동기화 확인
- [ ] `SubscriptionProvider` 상태가 `pro`로 변경 확인
- [ ] 제한된 기능들 접근 가능 확인:
  - [ ] 세트 할인 기능
  - [ ] 서버 연동 기능
  - [ ] 판매자별 관리
  - [ ] PDF/엑셀 내보내기
  - [ ] 고급 통계
- [ ] UI에서 "프로 플랜" 표시 확인

### 4. 정기결제 스케줄링 확인
- [ ] Cloud Scheduler 작업 상태 확인:
  - `daily-subscription-payment` (매일 00:00)
- [ ] Functions 로그에서 정기결제 처리 확인
- [ ] `processDailySubscriptions` 함수 정상 작동 확인

### 5. 구독 취소 플로우
- [ ] 앱에서 구독 취소 요청
- [ ] 상태 변경 확인: `status: 'cancel_scheduled'`
- [ ] 다음 결제일까지 프로 기능 유지 확인
- [ ] 결제일 이후 무료 플랜으로 전환 확인

## 🔧 테스트 도구

### Firebase Console 확인 항목
1. **Firestore Database**
   - `users/{uid}/subscription` 문서
   - `users/{uid}/cards` 컬렉션
   - `payment_history` 컬렉션

2. **Cloud Functions 로그**
   - `processDailySubscriptions` 실행 로그
   - `adminToggleSubscription` 로그

3. **Cloud Scheduler**
   - `daily-subscription-payment` 작업 상태

### 관리자 대시보드 확인
- URL: https://parabara-1a504.web.app/admin-dashboard
- 사용자 목록에서 구독 상태 확인
- 구독 전환 버튼으로 수동 테스트 가능

## ⚠️ 주의사항

### 테스트 환경
- 나이스페이 샌드박스 환경 사용
- 실제 결제 발생하지 않음
- 테스트 카드번호 사용 필수

### 실제 운영 전 확인사항
- [ ] 나이스페이 운영 환경으로 전환
- [ ] 실제 결제 금액 4900원 확인
- [ ] 정기결제 스케줄 정상 작동 확인
- [ ] 에러 처리 및 재시도 로직 확인

## 🐛 문제 발생 시 확인사항

1. **결제 실패**
   - 나이스페이 API 응답 확인
   - 카드 정보 유효성 확인
   - 네트워크 연결 상태 확인

2. **구독 상태 미반영**
   - Firestore 권한 설정 확인
   - SubscriptionService 동기화 확인
   - 앱 재시작 후 재확인

3. **프로 기능 미활성화**
   - FeatureAccessProvider 상태 확인
   - 구독 상태 동기화 확인
   - 캐시 클리어 후 재확인

## 📞 지원
문제 발생 시 개발팀에 다음 정보와 함께 연락:
- 사용자 UID
- 에러 메시지
- Firebase Functions 로그
- 재현 단계
