// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_settings.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_UserSettings _$UserSettingsFromJson(Map<String, dynamic> json) =>
    _UserSettings(
      lastWorkspaceId: (json['lastWorkspaceId'] as num?)?.toInt(),
      updatedAt: json['updatedAt'] == null
          ? null
          : DateTime.parse(json['updatedAt'] as String),
      deviceId: json['deviceId'] as String?,
      realtimeSyncEnabled: json['realtimeSyncEnabled'] as bool? ?? false,
      subscriptionPlanType:
          $enumDecodeNullable(
            _$SubscriptionPlanTypeEnumMap,
            json['subscriptionPlanType'],
          ) ??
          SubscriptionPlanType.free,
    );

Map<String, dynamic> _$UserSettingsToJson(_UserSettings instance) =>
    <String, dynamic>{
      'lastWorkspaceId': instance.lastWorkspaceId,
      'updatedAt': instance.updatedAt?.toIso8601String(),
      'deviceId': instance.deviceId,
      'realtimeSyncEnabled': instance.realtimeSyncEnabled,
      'subscriptionPlanType':
          _$SubscriptionPlanTypeEnumMap[instance.subscriptionPlanType]!,
    };

const _$SubscriptionPlanTypeEnumMap = {
  SubscriptionPlanType.free: 'free',
  SubscriptionPlanType.plus: 'plus',
  SubscriptionPlanType.pro: 'pro',
};
