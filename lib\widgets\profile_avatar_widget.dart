import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/nickname.dart';
import '../providers/nickname_provider.dart';

/// 프로필 아바타 위젯의 파일 존재 캐시
class _ProfileImageCache {
  static final Map<String, bool> _cache = {};
  static final Map<String, DateTime> _cacheTime = {};
  static const Duration _cacheExpiry = Duration(minutes: 5); // 5분 캐시

  static bool? getCachedExists(String path) {
    // 자동 정리 수행
    _cleanupExpiredEntries();
    
    final cachedTime = _cacheTime[path];
    if (cachedTime != null && 
        DateTime.now().difference(cachedTime) < _cacheExpiry) {
      return _cache[path];
    }
    return null;
  }

  static void setCachedExists(String path, bool exists) {
    _cache[path] = exists;
    _cacheTime[path] = DateTime.now();
    
    // 주기적 정리 (매 10번째 설정 시)
    if (_cache.length % 10 == 0) {
      _cleanupExpiredEntries();
    }
  }

  /// 만료된 캐시 항목들 자동 정리
  static void _cleanupExpiredEntries() {
    final now = DateTime.now();
    final expiredKeys = <String>[];
    
    for (final entry in _cacheTime.entries) {
      if (now.difference(entry.value) >= _cacheExpiry) {
        expiredKeys.add(entry.key);
      }
    }
    
    for (final key in expiredKeys) {
      _cache.remove(key);
      _cacheTime.remove(key);
    }
  }
}

/// 프로필 아바타 위젯
/// 
/// 로컬 이미지를 우선적으로 표시하고, 없으면 기본 아이콘을 표시합니다.
/// NicknameProvider와 연동하여 일관된 프로필 이미지 표시를 제공합니다.
class ProfileAvatarWidget extends ConsumerWidget {
  final double radius;
  final Color? backgroundColor;
  final Color? iconColor;
  final IconData defaultIcon;
  final Nickname? nickname;
  final bool showBorder;
  final Color? borderColor;
  final double borderWidth;

  const ProfileAvatarWidget({
    super.key,
    this.radius = 24.0,
    this.backgroundColor,
    this.iconColor,
    this.defaultIcon = Icons.person,
    this.nickname,
    this.showBorder = false,
    this.borderColor,
    this.borderWidth = 2.0,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // nickname이 직접 전달되지 않았으면 provider에서 가져오기
    final currentNickname = nickname ?? ref.watch(nicknameProvider);
    
    return Container(
      decoration: showBorder ? BoxDecoration(
        shape: BoxShape.circle,
        border: Border.all(
          color: borderColor ?? Theme.of(context).colorScheme.primary,
          width: borderWidth,
        ),
      ) : null,
      child: _buildAvatar(context, currentNickname),
    );
  }

  Widget _buildAvatar(BuildContext context, Nickname? nickname) {
    // 로컬 이미지가 있는 경우
    if (nickname?.profileImagePath != null &&
        nickname!.profileImagePath!.isNotEmpty) {
      final localFile = File(nickname.profileImagePath!);
      final filePath = nickname.profileImagePath!;

      // 캐시 확인
      final cachedExists = _ProfileImageCache.getCachedExists(filePath);
      if (cachedExists != null) {
        if (cachedExists) {
          return CircleAvatar(
            radius: radius,
            backgroundColor: backgroundColor ?? Colors.grey[200],
            backgroundImage: FileImage(localFile),
            key: ValueKey(nickname.profileImagePath),
            onBackgroundImageError: (exception, stackTrace) {
              // 캐시 무효화
              _ProfileImageCache.setCachedExists(filePath, false);
            },
          );
        } else {
          return _buildDefaultAvatar(context);
        }
      }

      // 캐시에 없으면 파일 존재 여부 확인 (로그 제거)
      return FutureBuilder<bool>(
        future: localFile.exists(),
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            // 로딩 중
            return CircleAvatar(
              radius: radius,
              backgroundColor: backgroundColor ?? Colors.grey[200],
              child: SizedBox(
                width: radius * 0.6,
                height: radius * 0.6,
                child: const CircularProgressIndicator(strokeWidth: 2),
              ),
            );
          }

          final exists = snapshot.data == true;
          // 결과를 캐시에 저장
          _ProfileImageCache.setCachedExists(filePath, exists);

          if (exists) {
            return CircleAvatar(
              radius: radius,
              backgroundColor: backgroundColor ?? Colors.grey[200],
              backgroundImage: FileImage(localFile),
              key: ValueKey(nickname.profileImagePath),
              onBackgroundImageError: (exception, stackTrace) {
                // 캐시 무효화
                _ProfileImageCache.setCachedExists(filePath, false);
              },
            );
          } else {
            return _buildDefaultAvatar(context);
          }
        },
      );
    }

    // 기본 아이콘 표시
    return _buildDefaultAvatar(context);
  }

  Widget _buildDefaultAvatar(BuildContext context) {
    return CircleAvatar(
      radius: radius,
      backgroundColor: backgroundColor ?? Colors.grey[200],
      child: Icon(
        defaultIcon,
        size: radius * 0.8,
        color: iconColor ?? Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
      ),
    );
  }
}

/// 편의를 위한 프리셋 프로필 아바타들
class ProfileAvatars {
  /// 작은 프로필 아바타 (반지름 16)
  static Widget small({
    Key? key,
    Nickname? nickname,
    Color? backgroundColor,
    Color? iconColor,
  }) => ProfileAvatarWidget(
    key: key,
    radius: 16.0,
    nickname: nickname,
    backgroundColor: backgroundColor,
    iconColor: iconColor,
  );

  /// 중간 프로필 아바타 (반지름 24)
  static Widget medium({
    Key? key,
    Nickname? nickname,
    Color? backgroundColor,
    Color? iconColor,
  }) => ProfileAvatarWidget(
    key: key,
    radius: 24.0,
    nickname: nickname,
    backgroundColor: backgroundColor,
    iconColor: iconColor,
  );

  /// 큰 프로필 아바타 (반지름 32)
  static Widget large({
    Key? key,
    Nickname? nickname,
    Color? backgroundColor,
    Color? iconColor,
    bool showBorder = false,
    Color? borderColor,
  }) => ProfileAvatarWidget(
    key: key,
    radius: 32.0,
    nickname: nickname,
    backgroundColor: backgroundColor,
    iconColor: iconColor,
    showBorder: showBorder,
    borderColor: borderColor,
  );

  /// 매우 큰 프로필 아바타 (반지름 48)
  static Widget extraLarge({
    Key? key,
    Nickname? nickname,
    Color? backgroundColor,
    Color? iconColor,
    bool showBorder = false,
    Color? borderColor,
  }) => ProfileAvatarWidget(
    key: key,
    radius: 48.0,
    nickname: nickname,
    backgroundColor: backgroundColor,
    iconColor: iconColor,
    showBorder: showBorder,
    borderColor: borderColor,
  );
}
