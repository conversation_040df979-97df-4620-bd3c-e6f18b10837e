rules_version = '2';

service firebase.storage {
  match /b/{bucket}/o {
    // === 사용자별 데이터 (클라이언트 권한 관리 지원) ===
    match /users/{userId}/{allPaths=**} {
      // 인증된 사용자만 접근 가능 (소유자 + 초대받은 사용자)
      // 실제 권한 검증은 클라이언트에서 수행
      allow read, write: if request.auth != null;

      // 파일 업로드 시 기본 크기 제한 (50MB)
      allow write: if request.auth != null
        && request.resource.size < 50 * 1024 * 1024;
    }

    // === 행사 이미지 (세부 경로별 최적화) ===
    match /users/{userId}/events/{eventId}/{filename} {
      allow read, write: if request.auth != null;

      // 이미지 파일만 허용, 크기 제한 (10MB)
      allow write: if request.auth != null
        && request.resource.contentType.matches('image/.*')
        && request.resource.size < 10 * 1024 * 1024;
    }

    // === 상품 이미지 ===
    match /users/{userId}/events/{eventId}/products/{filename} {
      allow read, write: if request.auth != null;

      // 이미지 파일만 허용, 크기 제한 (5MB)
      allow write: if request.auth != null
        && request.resource.contentType.matches('image/.*')
        && request.resource.size < 5 * 1024 * 1024;
    }

    // === 판매자 프로필 이미지 ===
    match /users/{userId}/events/{eventId}/sellers/{sellerId}/{filename} {
      allow read, write: if request.auth != null;

      // 이미지 파일만 허용, 크기 제한 (2MB)
      allow write: if request.auth != null
        && request.resource.contentType.matches('image/.*')
        && request.resource.size < 2 * 1024 * 1024;
    }

    // === 사용자 프로필 이미지 ===
    match /users/{userId}/profile/{filename} {
      allow read, write: if request.auth != null;

      // 이미지 파일만 허용, 크기 제한 (2MB)
      allow write: if request.auth != null
        && request.resource.contentType.matches('image/.*')
        && request.resource.size < 2 * 1024 * 1024;
    }

    // === 내보내기 파일 (PDF, Excel, CSV) ===
    match /users/{userId}/exports/{filename} {
      allow read, write: if request.auth != null;

      // 문서 파일만 허용, 크기 제한 (20MB)
      allow write: if request.auth != null
        && (request.resource.contentType.matches('application/pdf') ||
            request.resource.contentType.matches('application/vnd.ms-excel') ||
            request.resource.contentType.matches('application/vnd.openxmlformats-officedocument.spreadsheetml.sheet') ||
            request.resource.contentType.matches('text/csv'))
        && request.resource.size < 20 * 1024 * 1024;
    }

    // === 백업 파일 ===
    match /users/{userId}/backups/{filename} {
      allow read, write: if request.auth != null;

      // 압축 파일 및 JSON 파일만 허용, 크기 제한 (100MB)
      allow write: if request.auth != null
        && (request.resource.contentType.matches('application/zip') ||
            request.resource.contentType.matches('application/json') ||
            request.resource.contentType.matches('application/x-zip-compressed'))
        && request.resource.size < 100 * 1024 * 1024;
    }

    // === 임시 파일 (24시간 후 자동 삭제 예정) ===
    match /temp/{userId}/{filename} {
      allow read, write: if request.auth != null && request.auth.uid == userId;

      // 임시 파일 크기 제한 (10MB)
      allow write: if request.auth != null
        && request.auth.uid == userId
        && request.resource.size < 10 * 1024 * 1024;
    }

    // === 관리자 전용 파일 ===
    match /admin/{allPaths=**} {
      allow read, write: if request.auth != null &&
        request.auth.token.email in ['<EMAIL>'];
    }

    // === 공개 읽기 전용 파일 (앱 리소스, 업데이트 정보 등) ===
    match /public/{allPaths=**} {
      allow read: if true;
      allow write: if request.auth != null &&
        request.auth.token.email in ['<EMAIL>'];
    }

    // === 기본 거부 규칙 ===
    // 위에서 명시적으로 허용되지 않은 모든 접근은 거부
    match /{allPaths=**} {
      allow read, write: if false;
    }
  }
}
