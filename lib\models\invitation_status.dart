/// 바라 부스 매니저 - 초대 상태 열거형
///
/// 행사 초대의 상태를 나타내는 열거형입니다.
/// - pending: 초대 대기 중
/// - accepted: 초대 수락됨
/// - rejected: 초대 거절됨
/// - expired: 초대 만료됨
///
/// 작성자: Blue
/// 버전: 1.0.0
/// 최종 업데이트: 2025년 8월
library;

/// 초대 상태를 나타내는 열거형
enum InvitationStatus {
  /// 초대 대기 중
  pending('pending', '대기 중'),
  
  /// 초대 수락됨
  accepted('accepted', '수락됨'),
  
  /// 초대 거절됨
  rejected('rejected', '거절됨'),
  
  /// 초대 만료됨
  expired('expired', '만료됨');

  const InvitationStatus(this.value, this.displayName);

  /// 데이터베이스 저장용 값
  final String value;
  
  /// 사용자에게 표시할 이름
  final String displayName;

  /// 문자열 값으로부터 InvitationStatus 생성
  static InvitationStatus fromString(String value) {
    switch (value) {
      case 'pending':
        return InvitationStatus.pending;
      case 'accepted':
        return InvitationStatus.accepted;
      case 'rejected':
        return InvitationStatus.rejected;
      case 'expired':
        return InvitationStatus.expired;
      default:
        throw ArgumentError('Unknown invitation status: $value');
    }
  }

  /// JSON 직렬화를 위한 문자열 변환
  String toJson() => value;

  /// JSON 역직렬화를 위한 팩토리 생성자
  static InvitationStatus fromJson(String json) => fromString(json);
}
