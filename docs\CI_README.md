# 바라 부스 매니저 - CI/CD 파이프라인 가이드

## 개요

바라 부스 매니저 프로젝트는 GitHub Actions를 기반으로 한 자동화된 CI/CD 파이프라인을 구축했습니다. 이 파이프라인은 코드 품질, 테스트, 빌드, 성능, 보안을 자동으로 검증합니다.

## CI 파이프라인 구성

### 1. GitHub Actions 워크플로우 (`.github/workflows/ci.yml`)

#### 실행 조건
- `main`, `develop` 브랜치에 push
- Pull Request 생성/업데이트

#### 작업 구성
1. **Code Quality Check**
   - 코드 분석 (`flutter analyze`)
   - 포맷 검사 (`dart format`)
   - 커스텀 린트 검사

2. **Test Execution**
   - 단위 테스트 실행 (`flutter test --coverage`)
   - 테스트 커버리지 측정
   - Codecov 업로드

3. **Build Verification**
   - 다중 플랫폼 빌드 검증
   - Android, iOS, Web, Windows, macOS, Linux

4. **Performance Test**
   - 메모리 관리 테스트
   - 배치 프로세서 테스트

5. **Security Scan**
   - 의존성 보안 검사
   - 취약점 스캔

6. **CI Summary**
   - 모든 작업 결과 요약
   - 실패 시 상세 정보 제공

## 로컬 CI 실행

### Windows 환경 (PowerShell)

```powershell
# 전체 CI 실행
.\scripts\ci_local.ps1

# 빌드 제외하고 실행 (빠른 검사)
.\scripts\ci_local.ps1 -SkipBuild

# 상세 정보 출력
.\scripts\ci_local.ps1 -Verbose
```

### Linux/macOS 환경 (Bash)

```bash
# 실행 권한 부여
chmod +x scripts/ci_local.sh

# 전체 CI 실행
./scripts/ci_local.sh
```

## Pre-commit 훅 설정

커밋 전 자동 검사를 위해 pre-commit 훅을 설정할 수 있습니다.

### 1. Git 훅 설정

```bash
# pre-commit 훅 복사
cp scripts/pre-commit.sh .git/hooks/pre-commit
chmod +x .git/hooks/pre-commit
```

### 2. Pre-commit 검사 항목
- 코드 포맷 검사
- 코드 분석
- 빠른 테스트 실행 (주요 테스트만)
- 커스텀 린트 검사

## 의존성 자동 업데이트

### Dependabot 설정 (`.github/dependabot.yml`)

- **Flutter/Dart 패키지**: 매주 월요일 오전 9시
- **GitHub Actions**: 매주 월요일 오전 9시
- **주요 패키지**: 수동 업데이트 (안정성 보장)

### 무시되는 패키지
- `flutter`, `dart` (핵심 SDK)
- `riverpod` 관련 패키지들
- `sqflite`, `freezed`, `json_annotation`
- `mockito` (테스트 프레임워크)

## CI 파이프라인 장점

### 1. 개발 효율성
- 코드 커밋 시 자동 검증
- 조기 오류 발견
- 일관된 코드 품질 유지

### 2. 품질 보장
- 374개 테스트 자동 실행
- 코드 스타일 일관성
- 보안 취약점 조기 발견

### 3. 팀 협업
- Pull Request 자동 검증
- 코드 리뷰 프로세스 지원
- 충돌 조기 발견

### 4. 성능 모니터링
- 메모리 사용량 추적
- 배치 처리 성능 검증
- 빌드 시간 최적화

## 문제 해결

### CI 실패 시 대응

1. **테스트 실패**
   ```bash
   # 로컬에서 테스트 실행
   flutter test
   
   # 특정 테스트만 실행
   flutter test test/utils/memory_manager_test.dart
   ```

2. **코드 분석 오류**
   ```bash
   # 코드 분석 실행
   flutter analyze
   
   # 오류 수정 후 재실행
   ```

3. **포맷 문제**
   ```bash
   # 코드 포맷 수정
   dart format .
   
   # 포맷 검사
   dart format --set-exit-if-changed .
   ```

4. **빌드 실패**
   ```bash
   # 웹 빌드
   flutter build web
   
   # Windows 빌드
   flutter build windows
   ```

### 로그 확인

- GitHub Actions: Actions 탭에서 상세 로그 확인
- 로컬 CI: 터미널 출력에서 오류 메시지 확인
- 커스텀 린트: `custom_lint.log` 파일 확인

## 성능 최적화

### CI 실행 시간 단축

1. **병렬 실행**: GitHub Actions에서 여러 작업을 병렬로 실행
2. **캐싱**: 의존성 캐시 활용
3. **선택적 실행**: 변경된 파일에 따라 필요한 테스트만 실행

### 로컬 개발 최적화

1. **Pre-commit 훅**: 커밋 전 빠른 검사
2. **SkipBuild 옵션**: 빌드 제외하고 빠른 검사
3. **선택적 테스트**: 개발 중 필요한 테스트만 실행

## 모니터링 및 알림

### GitHub Actions 알림
- Pull Request 상태 확인
- 브랜치 보호 규칙 설정
- 실패 시 자동 알림

### 커버리지 추적
- Codecov 연동
- 테스트 커버리지 모니터링
- 커버리지 감소 시 알림

## 향후 계획

### 1. CD (Continuous Deployment) 추가
- 자동 배포 파이프라인
- 스테이징 환경 배포
- 롤백 기능

### 2. 고급 테스트
- 통합 테스트
- E2E 테스트
- 성능 벤치마크

### 3. 보안 강화
- 정적 분석 도구 추가
- 취약점 스캔 강화
- 의존성 보안 검사 강화

---

**참고**: 이 CI 파이프라인은 바라 부스 매니저 프로젝트의 안정성과 품질을 보장하기 위해 지속적으로 개선되고 있습니다. 
