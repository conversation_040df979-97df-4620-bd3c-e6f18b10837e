import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../models/prepayment.dart';
import '../../models/product.dart';
import '../../models/prepayment_product_link.dart';
import '../../models/prepayment_virtual_product.dart';
import '../../providers/prepayment_provider.dart';
import '../../providers/product_provider.dart';
import '../../providers/prepayment_product_link_provider.dart';
import '../../providers/prepayment_virtual_product_provider.dart';
import '../../providers/category_provider.dart';
import '../../providers/unified_workspace_provider.dart';
import '../../utils/event_workspace_utils.dart';
import '../../services/database_service.dart';
import '../inventory/inventory_screen.dart';
import '../../widgets/app_bar_styles.dart';

class PrepaymentProductLinkScreen extends ConsumerStatefulWidget {
  const PrepaymentProductLinkScreen({Key? key}) : super(key: key);

  @override
  ConsumerState<PrepaymentProductLinkScreen> createState() => _PrepaymentProductLinkScreenState();
}

class _PrepaymentProductLinkScreenState extends ConsumerState<PrepaymentProductLinkScreen> {
  int? _selectedVirtualProductId;
  int? _selectedProductId;
  bool _isLoading = true;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _refreshAllData();
    });
  }

  Future<void> _refreshAllData() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });
    try {
      await ref.read(prepaymentNotifierProvider.notifier).loadPrepayments();
      await ref.read(productNotifierProvider.notifier).loadProducts();
      await ref.read(prepaymentProductLinkNotifierProvider.notifier).loadLinks();
      await ref.read(prepaymentVirtualProductNotifierProvider.notifier).loadVirtualProducts();
      setState(() => _isLoading = false);
    } catch (e) {
      setState(() {
        _isLoading = false;
        _errorMessage = e.toString();
      });
      _showErrorDialog(context, e.toString());
    }
  }

  void _showErrorDialog(BuildContext context, String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('데이터 로딩 오류'),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('확인'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Scaffold(
        body: Center(child: CircularProgressIndicator()),
      );
    }
    if (_errorMessage != null) {
      String displayError = _errorMessage!;
      if (displayError.contains('no such table') || displayError.contains('database is not open')) {
        displayError = '데이터베이스 초기화 중입니다. 잠시 후 다시 시도해주세요.';
      }
      return Scaffold(
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(Icons.error, color: Colors.red, size: 48),
              const SizedBox(height: 16),
              Text('데이터 로딩 중 오류 발생', style: TextStyle(fontSize: 18, color: Colors.red)),
              const SizedBox(height: 8),
              Text(displayError, textAlign: TextAlign.center),
              const SizedBox(height: 24),
              ElevatedButton(
                onPressed: _refreshAllData,
                child: const Text('다시 시도'),
              ),
            ],
          ),
        ),
      );
    }
    final prepayments = ref.watch(prepaymentNotifierProvider).prepayments;
    final products = ref.watch(productNotifierProvider).products;
    final links = ref.watch(prepaymentProductLinkNotifierProvider);
    final virtualProducts = ref.watch(prepaymentVirtualProductNotifierProvider).virtualProducts;

    return Scaffold(
      appBar: AppBar(
        title: Builder(builder: (ctx)=> Text('선입금-상품 연동 관리', style: AppBarStyles.of(ctx))),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _refreshAllData,
            tooltip: '새로고침',
          ),
        ],
      ),
      body: Column(
        children: [
          // 통계 정보 - 반응형 디자인 적용
          Container(
            padding: const EdgeInsets.all(16),
            child: _buildResponsiveStatCards(virtualProducts, products, links),
          ),
          const Divider(),
          // 메인 컨텐츠 - 반응형 디자인 적용
          Expanded(
            child: _buildResponsiveMainContent(prepayments, products, links),
          ),
        ],
      ),
    );
  }

  // 선택된 가상 상품명을 가져오는 메서드
  String _getSelectedVirtualProductName() {
    if (_selectedVirtualProductId == null) return '';
    
    final virtualProducts = ref.read(prepaymentVirtualProductNotifierProvider).virtualProducts;
    
    PrepaymentVirtualProduct? selectedProduct;
    try {
      selectedProduct = virtualProducts.firstWhere(
        (p) => p.id == _selectedVirtualProductId,
      );
    } catch (e) {
      return '알수없음';
    }
    return selectedProduct.name;
  }

  // 이미 연동된 상품인지 확인하는 메서드
  bool _isAlreadyLinked(int? virtualProductId, int? productId, List<PrepaymentProductLink> links) {
    if (virtualProductId == null || productId == null) return false;
    return links.any((link) => link.virtualProductId == virtualProductId && link.productId == productId);
  }

  // 이미 연동된 상품인지 확인하는 메서드 (상품 기준)
  bool _isProductAlreadyLinked(int? productId, List<PrepaymentProductLink> links) {
    if (productId == null) return false;
    return links.any((link) => link.productId == productId);
  }

  // 반응형 통계 카드들
  Widget _buildResponsiveStatCards(List<PrepaymentVirtualProduct> virtualProducts, List<Product> products, List<PrepaymentProductLink> links) {
    return LayoutBuilder(
      builder: (context, constraints) {
        // 화면 크기에 따라 레이아웃 결정
        final isWideScreen = constraints.maxWidth > 600;
        
        final cards = [
          _buildStatCard(
            '선입금 데이터 개수',
            '${virtualProducts.length}개',
            Icons.shopping_cart,
            Colors.blue,
          ),
          _buildStatCard(
            '상품 개수',
            '${products.length}개',
            Icons.inventory,
            Colors.green,
          ),
          _buildStatCard(
            '연동된 데이터',
            '${links.length}개',
            Icons.link,
            Colors.orange,
          ),
        ];
        
        if (isWideScreen) {
          // 넓은 화면에서는 가로로 배치
          return Row(
            children: [
              Expanded(child: cards[0]),
              const SizedBox(width: 16),
              Expanded(child: cards[1]),
              const SizedBox(width: 16),
              Expanded(child: cards[2]),
            ],
          );
        } else {
          // 좁은 화면에서는 세로로 배치
          return Column(
            children: [
              cards[0],
              const SizedBox(height: 8),
              cards[1],
              const SizedBox(height: 8),
              cards[2],
            ],
          );
        }
      },
    );
  }

  // 반응형 메인 컨텐츠
  Widget _buildResponsiveMainContent(List<Prepayment> prepayments, List<Product> products, List<PrepaymentProductLink> links) {
    return LayoutBuilder(
      builder: (context, constraints) {
        // 화면 크기에 따라 레이아웃 결정
        final isWideScreen = constraints.maxWidth > 800;
        
        if (isWideScreen) {
          // 넓은 화면에서는 가로로 배치 (기존 방식)
          return Row(
            children: [
              // 선입금 목록
              Expanded(
                flex: 2,
                child: _buildPrepaymentList(prepayments, links),
              ),
              const VerticalDivider(),
              // 상품 목록
              Expanded(
                flex: 2,
                child: _buildProductList(products, links),
              ),
              const VerticalDivider(),
              // 연동 관리
              Expanded(
                flex: 3,
                child: _buildLinkManagement(prepayments, products, links),
              ),
            ],
          );
        } else {
          // 좁은 화면에서는 탭으로 구성
          return DefaultTabController(
            length: 3,
            child: Column(
              children: [
                TabBar(
                  labelStyle: Theme.of(context).textTheme.bodyMedium!.copyWith(fontFamily: 'Pretendard', fontSize: 12),
                  unselectedLabelStyle: Theme.of(context).textTheme.bodyMedium!.copyWith(fontFamily: 'Pretendard', fontSize: 12),
                  tabs: [
                    Tab(text: '선입금 상품'),
                    Tab(text: '상품 목록'),
                    Tab(text: '연동 관리'),
                  ],
                ),
                Expanded(
                  child: TabBarView(
                    children: [
                      _buildPrepaymentList(prepayments, links),
                      _buildProductList(products, links),
                      _buildLinkManagement(prepayments, products, links),
                    ],
                  ),
                ),
              ],
            ),
          );
        }
      },
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          children: [
            Icon(icon, color: color, size: 28),
            const SizedBox(height: 6),
            Text(
              title,
              style: Theme.of(context).textTheme.bodyMedium!.copyWith(fontFamily: 'Pretendard', fontSize: 11, color: Colors.grey),
              textAlign: TextAlign.center,
            ),
            Text(
              value,
              style: Theme.of(context).textTheme.titleMedium!.copyWith(fontFamily: 'Pretendard', fontSize: 16, fontWeight: FontWeight.bold),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPrepaymentList(List<Prepayment> prepayments, List<PrepaymentProductLink> links) {
    final virtualProducts = ref.watch(prepaymentVirtualProductNotifierProvider).virtualProducts;
    
    // 연동 상태에 따라 정렬: 연동되지 않은 항목을 위로
    final sortedVirtualProducts = List<PrepaymentVirtualProduct>.from(virtualProducts);
    sortedVirtualProducts.sort((a, b) {
      final aLinked = links.any((link) => link.virtualProductId == a.id);
      final bLinked = links.any((link) => link.virtualProductId == b.id);
      if (aLinked == bLinked) return a.name.compareTo(b.name); // 같은 상태면 이름순
      return aLinked ? 1 : -1; // 연동되지 않은 항목을 위로
    });
    
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(12),
          child: Row(
            children: [
              const Icon(Icons.shopping_cart, color: Colors.green, size: 20),
              const SizedBox(width: 6),
              const Text(
                '선입금 상품',
                style: TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
              ),
              const Spacer(),
              Text(
                '${virtualProducts.length}개',
                style: Theme.of(context).textTheme.bodyMedium!.copyWith(fontFamily: 'Pretendard', fontSize: 12),
              ),
            ],
          ),
        ),
        Expanded(
          child: ListView.builder(
            itemCount: sortedVirtualProducts.length,
            itemBuilder: (context, index) {
              final product = sortedVirtualProducts[index];
              final isLinked = links.any((link) => link.virtualProductId == product.id);
              
              return Card(
                margin: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                child: Container(
                  decoration: isLinked ? BoxDecoration(
                    color: Colors.grey.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(4),
                  ) : null,
                  child: ListTile(
                    contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                    title: Text(
                      product.name,
                      style: TextStyle(
                        fontWeight: FontWeight.w500,
                        fontSize: 14,
                        color: isLinked ? Colors.grey[600] : null,
                      ),
                    ),
                    subtitle: Text(
                      '총 판매 수: ${product.quantity}개',
                      style: TextStyle(
                        fontSize: 12,
                        color: isLinked ? Colors.grey[500] : null,
                      ),
                    ),
                    selected: _selectedVirtualProductId == product.id,
                    onTap: () {
                      setState(() {
                        _selectedVirtualProductId = product.id;
                      });
                    },
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildProductList(List<Product> products, List<PrepaymentProductLink> links) {
    // 연동 상태에 따라 정렬: 연동되지 않은 항목을 위로
    final sortedProducts = List<Product>.from(products);
    sortedProducts.sort((a, b) {
      final aLinked = _isProductAlreadyLinked(a.id, links);
      final bLinked = _isProductAlreadyLinked(b.id, links);
      if (aLinked == bLinked) return a.name.compareTo(b.name); // 같은 상태면 이름순
      return aLinked ? 1 : -1; // 연동되지 않은 항목을 위로
    });
    
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(12),
          child: Row(
            children: [
              const Icon(Icons.inventory, color: Colors.green, size: 20),
              const SizedBox(width: 6),
              const Text(
                '상품 목록',
                style: TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
              ),
              const Spacer(),
              Text(
                '${products.length}개',
                style: Theme.of(context).textTheme.bodyMedium!.copyWith(fontFamily: 'Pretendard', fontSize: 12),
              ),
            ],
          ),
        ),
        Expanded(
          child: ListView.builder(
            itemCount: sortedProducts.length,
            itemBuilder: (context, index) {
              final product = sortedProducts[index];
              final isLinked = _isProductAlreadyLinked(product.id, links);
              
              return Card(
                margin: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                child: Container(
                  decoration: isLinked ? BoxDecoration(
                    color: Colors.grey.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(4),
                  ) : null,
                  child: ListTile(
                    contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                    title: Text(
                      product.name,
                      style: TextStyle(
                        fontWeight: FontWeight.w500,
                        fontSize: 14,
                        color: isLinked ? Colors.grey[600] : null,
                      ),
                    ),
                    subtitle: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          '가격: ${product.price}원',
                          style: TextStyle(
                            fontSize: 12,
                            color: isLinked ? Colors.grey[500] : null,
                          ),
                        ),
                        Text(
                          '재고: ${product.quantity}개',
                          style: TextStyle(
                            fontSize: 12,
                            color: isLinked ? Colors.grey[500] : null,
                          ),
                        ),
                      ],
                    ),
                    selected: _selectedProductId == product.id,
                    onTap: () {
                      setState(() {
                        _selectedProductId = product.id;
                      });
                    },
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildLinkManagement(
    List<Prepayment> prepayments,
    List<Product> products,
    List<PrepaymentProductLink> links,
  ) {
    final canLink = _selectedVirtualProductId != null && 
                   _selectedProductId != null && 
                   !_isAlreadyLinked(_selectedVirtualProductId!, _selectedProductId!, links) &&
                   !_isProductAlreadyLinked(_selectedProductId!, links);
    // 전체 자동 연동 버튼 추가
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(12),
          child: Row(
            children: [
              const Icon(Icons.link, color: Colors.orange, size: 20),
              const SizedBox(width: 6),
              const Text(
                '연동 관리',
                style: TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
              ),
              const Spacer(),
              Text(
                '${links.length}개',
                style: Theme.of(context).textTheme.bodyMedium!.copyWith(fontFamily: 'Pretendard', fontSize: 12),
              ),
            ],
          ),
        ),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: LayoutBuilder(
            builder: (context, constraints) {
              final isNarrowScreen = constraints.maxWidth < 400;
              
              if (isNarrowScreen) {
                // 좁은 화면에서는 세로로 배치
                return Column(
                  children: [
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton.icon(
                        onPressed: canLink
                            ? () async {
                                final databaseService = ref.read(databaseServiceProvider);
                                String? errorMsg;
                                try {
                                  // 선택된 가상상품과 상품의 id를 DB에서 정확히 매칭하여 연동
                                  final virtualProductId = _selectedVirtualProductId!;
                                  final productId = _selectedProductId!;
                                  // 이미 연동되어 있으면 중복 방지
                                  final existingLinks = await databaseService.database.then((db) => db.query(
                                    'prepayment_product_link',
                                    where: 'virtualProductId = ? AND productId = ?',
                                    whereArgs: [virtualProductId, productId],
                                  ));
                                  if (existingLinks.isEmpty) {
                                    final currentWorkspace = ref.read(currentWorkspaceProvider);
                                    if (currentWorkspace == null) {
                                      errorMsg = '현재 행사가 선택되지 않았습니다';
                                    } else {
                                      final currentEvent = EventWorkspaceUtils.workspaceToEvent(currentWorkspace);
                                      if (currentEvent == null) {
                                        errorMsg = '행사 정보를 불러올 수 없습니다';
                                      } else {
                                        await databaseService.database.then((db) => db.insert('prepayment_product_link', {
                                          'virtualProductId': virtualProductId,
                                          'productId': productId,
                                          'linkedAt': DateTime.now().toIso8601String(),
                                          'eventId': currentEvent.id ?? currentWorkspace.id,
                                        }));
                                      }
                                    }
                                  }
                                  // 연동 후 상태 동기화
                                  await ref.read(prepaymentProductLinkNotifierProvider.notifier).loadLinks();
                                } catch (e) {
                                  errorMsg = e.toString();
                                }
                                // 연동 결과 안내 다이얼로그
                                if (!mounted) return;
                                await showDialog(
                                  context: context,
                                  builder: (context) => AlertDialog(
                                    title: Text(errorMsg == null ? '연동 성공' : '연동 실패'),
                                    content: Text(errorMsg == null
                                        ? '1개의 연동 데이터가 생성되었습니다.'
                                        : '연동 중 오류가 발생했습니다: $errorMsg'),
                                    actions: [
                                      TextButton(
                                        onPressed: () {
                                          Navigator.of(context).pop();
                                          Navigator.of(context).pushAndRemoveUntil(
                                            MaterialPageRoute(builder: (context) => const InventoryScreen()),
                                            (route) => false,
                                          );
                                        },
                                        child: const Text('확인'),
                                      ),
                                    ],
                                  ),
                                );
                              }
                            : null,
                        icon: const Icon(Icons.link),
                        label: const Text('연동'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.green,
                          foregroundColor: Colors.white,
                        ),
                      ),
                    ),
                    const SizedBox(height: 8),
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton.icon(
                        onPressed: (_selectedVirtualProductId != null && _selectedProductId != null)
                            ? () async {
                                await ref.read(prepaymentProductLinkNotifierProvider.notifier)
                                    .removeLink(_selectedVirtualProductId!, _selectedProductId!);
                              }
                            : null,
                        icon: const Icon(Icons.link_off),
                        label: const Text('연동 해제'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.red,
                          foregroundColor: Colors.white,
                        ),
                      ),
                    ),
                  ],
                );
              } else {
                // 넓은 화면에서는 가로로 배치 (기존 방식)
                return Row(
                  children: [
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: canLink
                            ? () async {
                                final databaseService = ref.read(databaseServiceProvider);
                                String? errorMsg;
                                try {
                                  // 선택된 가상상품과 상품의 id를 DB에서 정확히 매칭하여 연동
                                  final virtualProductId = _selectedVirtualProductId!;
                                  final productId = _selectedProductId!;
                                  // 이미 연동되어 있으면 중복 방지
                                  final existingLinks = await databaseService.database.then((db) => db.query(
                                    'prepayment_product_link',
                                    where: 'virtualProductId = ? AND productId = ?',
                                    whereArgs: [virtualProductId, productId],
                                  ));
                                  if (existingLinks.isEmpty) {
                                    final currentWorkspace = ref.read(currentWorkspaceProvider);
                                    if (currentWorkspace == null) {
                                      errorMsg = '현재 행사가 선택되지 않았습니다';
                                    } else {
                                      final currentEvent = EventWorkspaceUtils.workspaceToEvent(currentWorkspace);
                                      if (currentEvent == null) {
                                        errorMsg = '행사 정보를 불러올 수 없습니다';
                                      } else {
                                        await databaseService.database.then((db) => db.insert('prepayment_product_link', {
                                          'virtualProductId': virtualProductId,
                                          'productId': productId,
                                          'linkedAt': DateTime.now().toIso8601String(),
                                          'eventId': currentEvent.id ?? currentWorkspace.id,
                                        }));
                                      }
                                    }
                                  }
                                  // 연동 후 상태 동기화
                                  await ref.read(prepaymentProductLinkNotifierProvider.notifier).loadLinks();
                                } catch (e) {
                                  errorMsg = e.toString();
                                }
                                // 연동 결과 안내 다이얼로그
                                if (!mounted) return;
                                await showDialog(
                                  context: context,
                                  builder: (context) => AlertDialog(
                                    title: Text(errorMsg == null ? '연동 성공' : '연동 실패'),
                                    content: Text(errorMsg == null
                                        ? '1개의 연동 데이터가 생성되었습니다.'
                                        : '연동 중 오류가 발생했습니다: $errorMsg'),
                                    actions: [
                                      TextButton(
                                        onPressed: () {
                                          Navigator.of(context).pop();
                                          Navigator.of(context).pushAndRemoveUntil(
                                            MaterialPageRoute(builder: (context) => const InventoryScreen()),
                                            (route) => false,
                                          );
                                        },
                                        child: const Text('확인'),
                                      ),
                                    ],
                                  ),
                                );
                              }
                            : null,
                        icon: const Icon(Icons.link),
                        label: const Text('연동'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.green,
                          foregroundColor: Colors.white,
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: (_selectedVirtualProductId != null && _selectedProductId != null)
                            ? () async {
                                await ref.read(prepaymentProductLinkNotifierProvider.notifier)
                                    .removeLink(_selectedVirtualProductId!, _selectedProductId!);
                              }
                            : null,
                        icon: const Icon(Icons.link_off),
                        label: const Text('연동 해제'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.red,
                          foregroundColor: Colors.white,
                        ),
                      ),
                    ),
                  ],
                );
              }
            },
          ),
        ),
        // 선택된 항목 정보
        if (_selectedVirtualProductId != null || _selectedProductId != null)
          Container(
            padding: const EdgeInsets.all(12),
            margin: const EdgeInsets.symmetric(horizontal: 16),
            decoration: BoxDecoration(
              color: Colors.grey[100],
              borderRadius: BorderRadius.circular(8),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  '선택된 항목',
                  style: TextStyle(fontWeight: FontWeight.bold, fontSize: 14),
                ),
                if (_selectedVirtualProductId != null) ...[
                  const SizedBox(height: 6),
                  Text(
                    '가상 상품: ${_getSelectedVirtualProductName()}',
                    style: Theme.of(context).textTheme.bodyMedium!.copyWith(fontFamily: 'Pretendard', fontSize: 12),
                  ),
                ],
                if (_selectedProductId != null) ...[
                  const SizedBox(height: 4),
                  Text(
                    '상품: ${products.firstWhere((p) => p.id == _selectedProductId).name}',
                    style: Theme.of(context).textTheme.bodyMedium!.copyWith(fontFamily: 'Pretendard', fontSize: 12),
                  ),
                ],
                if (!canLink && (_selectedVirtualProductId != null && _selectedProductId != null)) ...[
                  const SizedBox(height: 6),
                  const Text(
                    '이미 연동된 상품이거나 선입금 데이터입니다.',
                    style: TextStyle(color: Colors.orange, fontSize: 11),
                  ),
                ],
              ],
            ),
          ),
        const SizedBox(height: 16),
        // 연동 현황
        Expanded(
          child: _buildGroupedLinksList(links, prepayments, products),
        ),
      ],
    );
  }

  /// 연동 목록을 표시하는 위젯
  Widget _buildGroupedLinksList(
    List<PrepaymentProductLink> links,
    List<Prepayment> prepayments,
    List<Product> products,
  ) {
    if (links.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.link_off, size: 48, color: Colors.grey),
            SizedBox(height: 12),
            Text(
              '연동된 데이터가 없습니다',
              style: TextStyle(fontSize: 14, color: Colors.grey),
            ),
            SizedBox(height: 6),
            Text(
              '선입금 데이터와 상품을 선택하여 연동하세요',
              style: TextStyle(fontSize: 12, color: Colors.grey),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      itemCount: links.length,
      itemBuilder: (context, index) {
        final link = links[index];
        
        // 가상 상품 목록에서 해당 prepaymentId와 매칭되는 상품 찾기
        final virtualProducts = ref.watch(prepaymentVirtualProductNotifierProvider).virtualProducts;
        final virtualProduct = virtualProducts.firstWhere(
          (vp) => vp.id == link.virtualProductId,
          orElse: () => PrepaymentVirtualProduct(
            id: 0,
            name: '알수없는 상품',
            price: 0,
            quantity: 0,
            createdAt: DateTime.now(),
            eventId: 1, // 기본값
          ),
        );
        
        final product = products.firstWhere(
          (p) => p.id == link.productId,
          orElse: () => Product(
            id: 0,
            name: '알수없는 상품',
            price: 0,
            quantity: 0,
          ),
        );

        // 카테고리 정보 가져오기
        final category = ref.read(categoryByIdProvider(product.categoryId));
        final productDisplayName = category != null ? '${category.name} - ${product.name}' : product.name;

        // 연동 이름 생성: '카테고리-상품이름 연동'
        final linkName = '$productDisplayName 연동';
        
        return Card(
          margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          child: ListTile(
            contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            title: Text(
              linkName,
              style: Theme.of(context).textTheme.bodyMedium!.copyWith(fontFamily: 'Pretendard', fontWeight: FontWeight.w500, fontSize: 14),
            ),
            subtitle: Text(
              '연동된 선입금 데이터: ${virtualProduct.name}',
              style: Theme.of(context).textTheme.bodyMedium!.copyWith(fontFamily: 'Pretendard', fontSize: 12),
            ),
            trailing: IconButton(
              icon: const Icon(Icons.delete, color: Colors.red, size: 20),
              onPressed: () async {
                await ref.read(prepaymentProductLinkNotifierProvider.notifier)
                    .removeLink(link.virtualProductId, link.productId);
              },
              tooltip: '연동 해제',
            ),
          ),
        );
      },
    );
  }

}



