/// 바라 부스 매니저 - 사용량 추적 서비스
///
/// 프로플랜 사용자의 서버 사용량을 추적하고 기록하는 서비스입니다.
/// - Firebase 작업 추적
/// - 서버 사용량 기록
/// - 플랜별 사용량 제한 확인
///
/// 작성자: Blue
/// 버전: 1.0.0
/// 최종 업데이트: 2025년 8월
library;

import 'dart:convert';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:http/http.dart' as http;
import '../utils/logger_utils.dart';
import '../models/subscription_plan.dart';
import 'subscription_service.dart';

/// 사용량 추적 서비스 클래스
class UsageTrackingService {
  static const String _tag = 'UsageTrackingService';
  static const String _baseUrl = 'https://us-central1-parabara-1a504.cloudfunctions.net';
  
  final SubscriptionService _subscriptionService = SubscriptionService();
  
  /// 싱글톤 인스턴스
  static final UsageTrackingService _instance = UsageTrackingService._internal();
  factory UsageTrackingService() => _instance;
  UsageTrackingService._internal();

  /// 서버 사용량 추적
  /// 
  /// [action] 수행한 작업 (예: 'upload', 'download', 'sync')
  /// [metadata] 추가 메타데이터 (선택사항)
  Future<void> trackUsage(String action, {Map<String, dynamic>? metadata}) async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) {
        LoggerUtils.logWarning('로그인되지 않은 사용자 - 사용량 추적 건너뛰기', tag: _tag);
        return;
      }

      // 프로플랜 사용자만 서버 사용량 추적
      final planType = await _subscriptionService.getCurrentPlanType();
      if (planType != SubscriptionPlanType.pro) {
        LoggerUtils.logDebug('프로플랜이 아닌 사용자 - 사용량 추적 건너뛰기: $planType', tag: _tag);
        return;
      }

      LoggerUtils.logDebug('서버 사용량 추적: $action', tag: _tag);

      // 서버에 사용량 추적 요청
      await _sendUsageTrackingRequest(user.uid, action, metadata);

    } catch (e) {
      LoggerUtils.logError('사용량 추적 실패', tag: _tag, error: e);
      // 사용량 추적 실패는 앱 기능에 영향을 주지 않도록 무시
    }
  }

  /// 서버에 사용량 추적 요청 전송
  Future<void> _sendUsageTrackingRequest(String userId, String action, Map<String, dynamic>? metadata) async {
    try {
      final response = await http.post(
        Uri.parse('$_baseUrl/trackUsage'),
        headers: {'Content-Type': 'application/json'},
        body: json.encode({
          'userId': userId,
          'action': action,
          'metadata': metadata ?? {},
          'timestamp': DateTime.now().toIso8601String(),
        }),
      ).timeout(const Duration(seconds: 10));

      if (response.statusCode != 200) {
        LoggerUtils.logWarning('사용량 추적 요청 실패: ${response.statusCode}', tag: _tag);
      } else {
        LoggerUtils.logDebug('사용량 추적 요청 성공: $action', tag: _tag);
      }
    } catch (e) {
      LoggerUtils.logError('사용량 추적 요청 오류', tag: _tag, error: e);
    }
  }

  /// Firebase 업로드 작업 추적
  Future<void> trackFirebaseUpload(String collection, {int? count, Map<String, dynamic>? metadata}) async {
    await trackUsage('firebase_upload', metadata: {
      'collection': collection,
      'count': count ?? 1,
      ...?metadata,
    });
  }

  /// Firebase 다운로드 작업 추적
  Future<void> trackFirebaseDownload(String collection, {int? count, Map<String, dynamic>? metadata}) async {
    await trackUsage('firebase_download', metadata: {
      'collection': collection,
      'count': count ?? 1,
      ...?metadata,
    });
  }

  /// Firebase 동기화 작업 추적
  Future<void> trackFirebaseSync(String operation, {Map<String, dynamic>? metadata}) async {
    await trackUsage('firebase_sync', metadata: {
      'operation': operation,
      ...?metadata,
    });
  }

  /// Storage 업로드 작업 추적
  Future<void> trackStorageUpload(String path, {int? fileSize, Map<String, dynamic>? metadata}) async {
    await trackUsage('storage_upload', metadata: {
      'path': path,
      'fileSize': fileSize,
      ...?metadata,
    });
  }

  /// Storage 다운로드 작업 추적
  Future<void> trackStorageDownload(String path, {int? fileSize, Map<String, dynamic>? metadata}) async {
    await trackUsage('storage_download', metadata: {
      'path': path,
      'fileSize': fileSize,
      ...?metadata,
    });
  }

  /// 실시간 동기화 작업 추적
  Future<void> trackRealtimeSync(String eventType, {Map<String, dynamic>? metadata}) async {
    await trackUsage('realtime_sync', metadata: {
      'eventType': eventType,
      ...?metadata,
    });
  }
}
