import 'dart:convert';
import 'package:equatable/equatable.dart';

/// 오프라인 작업 우선순위
enum TaskPriority {
  low(1),
  normal(2),
  high(3),
  critical(5);

  const TaskPriority(this.value);
  final int value;
}

/// 오프라인 작업(동기화 대기) 정보를 저장/관리하는 데이터 클래스입니다.
/// - 작업 유형(enum), 테이블명, 데이터, 타임스탬프 등 포함
/// - 오프라인 작업 큐/자동 동기화/네트워크 장애 복구 등 지원
class OfflineTask extends Equatable {
  final String id; // UUID 등 고유 식별자
  final OfflineTaskType type;
  final String table;
  final Map<String, dynamic> data;
  final DateTime timestamp;
  final bool isSynced; // 동기화 성공 여부
  
  // offline_sync_manager.dart에서 사용하는 고급 기능들
  final TaskPriority priority;
  final DateTime createdAt;
  final DateTime? scheduledAt;
  final int retryCount;
  final String? errorMessage;
  final Map<String, dynamic> metadata;

  const OfflineTask({
    required this.id,
    required this.type,
    required this.table,
    required this.data,
    required this.timestamp,
    this.isSynced = false,
    // 고급 기능들 (기본값 제공으로 기존 코드 호환성 유지)
    this.priority = TaskPriority.normal,
    DateTime? createdAt,
    this.scheduledAt,
    this.retryCount = 0,
    this.errorMessage,
    this.metadata = const {},
  }) : createdAt = createdAt ?? timestamp;

  OfflineTask copyWith({
    bool? isSynced,
    TaskPriority? priority,
    DateTime? scheduledAt,
    int? retryCount,
    String? errorMessage,
    Map<String, dynamic>? metadata,
  }) {
    return OfflineTask(
      id: id,
      type: type,
      table: table,
      data: data,
      timestamp: timestamp,
      isSynced: isSynced ?? this.isSynced,
      priority: priority ?? this.priority,
      createdAt: createdAt,
      scheduledAt: scheduledAt ?? this.scheduledAt,
      retryCount: retryCount ?? this.retryCount,
      errorMessage: errorMessage ?? this.errorMessage,
      metadata: metadata ?? this.metadata,
    );
  }

  /// 작업 복잡도 (우선순위 + 시간 기반)
  double get complexity {
    final priorityFactor = priority.value;
    final timeFactor = DateTime.now().difference(createdAt).inMinutes / 60.0;
    return priorityFactor * (1 + timeFactor);
  }

  /// 재시도 가능 여부
  bool get canRetry => retryCount < 3;

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'type': type.name,
      'table': table,
      'data': jsonEncode(data),
      'timestamp': timestamp.toIso8601String(),
      'isSynced': isSynced ? 1 : 0,
    };
  }

  /// JSON 직렬화 (offline_sync_manager.dart 호환성)
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type.name,
      'table': table,
      'data': data,
      'priority': priority.name,
      'createdAt': createdAt.toIso8601String(),
      'scheduledAt': scheduledAt?.toIso8601String(),
      'retryCount': retryCount,
      'errorMessage': errorMessage,
      'metadata': metadata,
    };
  }

  factory OfflineTask.fromMap(Map<String, dynamic> map) {
    return OfflineTask(
      id: map['id'] as String,
      type: OfflineTaskType.values.firstWhere((e) => e.name == map['type']),
      table: map['table'] as String,
      data: jsonDecode(map['data'] as String) as Map<String, dynamic>,
      timestamp: DateTime.parse(map['timestamp'] as String),
      isSynced: (map['isSynced'] ?? 0) == 1,
    );
  }

  /// JSON 역직렬화 (offline_sync_manager.dart 호환성)
  factory OfflineTask.fromJson(Map<String, dynamic> json) {
    return OfflineTask(
      id: json['id'],
      type: OfflineTaskType.values.firstWhere((e) => e.name == json['type']),
      table: json['table'],
      data: json['data'],
      timestamp: DateTime.parse(json['createdAt'] ?? json['timestamp']),
      priority: TaskPriority.values.firstWhere((e) => e.name == json['priority']),
      createdAt: DateTime.parse(json['createdAt']),
      scheduledAt: json['scheduledAt'] != null ? DateTime.parse(json['scheduledAt']) : null,
      retryCount: json['retryCount'] ?? 0,
      errorMessage: json['errorMessage'],
      metadata: json['metadata'] ?? {},
    );
  }

  @override
  List<Object?> get props => [id, type, table, data, timestamp, isSynced, priority, createdAt, scheduledAt, retryCount, errorMessage, metadata];
}

/// 오프라인 작업 유형(enum)입니다.
/// - insert/update/delete 등 DB 작업 구분 
/// - create/sync/upload/download 등 고급 동기화 작업 지원
/// - 오프라인 큐/동기화/네트워크 장애 복구 등에서 사용
enum OfflineTaskType { 
  insert, 
  update, 
  delete,
  create,   // offline_sync_manager.dart에서 사용 
  sync,     // offline_sync_manager.dart에서 사용
  upload,   // offline_sync_manager.dart에서 사용
  download  // offline_sync_manager.dart에서 사용
}

/// 오프라인 작업 큐 유틸리티 (메모리 기반, DB 연동은 별도 구현)
class OfflineTaskQueue {
  static final List<OfflineTask> _queue = [];

  static List<OfflineTask> get allTasks => List.unmodifiable(_queue);

  static void addTask(OfflineTask task) {
    _queue.add(task);
  }

  static void removeTask(String id) {
    _queue.removeWhere((t) => t.id == id);
  }

  static void clear() {
    _queue.clear();
  }

  static OfflineTask? getTask(String id) {
    return _firstWhereOrNull(_queue, (t) => t.id == id);
  }
}

T? _firstWhereOrNull<T>(List<T> list, bool Function(T) test) {
  for (final element in list) {
    if (test(element)) return element;
  }
  return null;
}
