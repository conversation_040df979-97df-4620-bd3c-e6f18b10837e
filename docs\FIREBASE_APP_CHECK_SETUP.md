# Firebase App Check 설정 가이드

## 개요
Firebase App Check는 앱의 무결성을 확인하여 악의적인 트래픽으로부터 Firebase 리소스를 보호합니다.

## 1. Firebase Console 설정

### 1.1 App Check 활성화
1. [Firebase Console](https://console.firebase.google.com/) 접속
2. 프로젝트 선택
3. 좌측 메뉴에서 **App Check** 클릭
4. **시작하기** 또는 **Apps** 탭 선택

### 1.2 앱 등록 확인
- Android 앱: `com.blue.parabara`
- iOS 앱: 번들 ID 확인 필요

### 1.3 디버그 토큰 설정

#### Android 디버그 토큰
1. Android 앱 섹션에서 **Debug tokens** 탭 클릭
2. **Add debug token** 클릭
3. 토큰 입력: `8393dade-0a44-4ad0-8aaf-dcc6727a078a` (실제 앱에서 생성됨)
4. 설명 추가: "Android 개발용 디버그 토큰"
5. **Save** 클릭

#### iOS 디버그 토큰
1. iOS 앱 섹션에서 **Debug tokens** 탭 클릭
2. **Add debug token** 클릭
3. 토큰 입력: `94180427-6269-4224-87D6-DC182E09EA70`
4. 설명 추가: "iOS 개발용 디버그 토큰"
5. **Save** 클릭

## 2. 앱 설정 확인

### 2.1 현재 설정 상태
- ✅ `firebase_app_check: ^0.3.2+9` 패키지 설치 완료
- ✅ main.dart에서 App Check 초기화 코드 추가 완료
- ✅ 디버그 모드로 설정 완료

### 2.2 설정된 기능
- Android Debug Provider 활성화
- iOS Debug Provider 활성화
- 자동 토큰 갱신 활성화
- 상태 모니터링 및 로깅

## 3. 테스트 방법

### 3.1 디버그 앱 실행
```bash
flutter run
```

### 3.2 로그 확인
앱 실행 시 다음 로그를 확인하세요:
```
[AppCheckUtils] === Firebase App Check 디버그 토큰 확인 방법 ===
[AppCheckUtils] Android용 디버그 토큰: 783023AA-6A8F-47D1-8D0A-F4F3CBE6C5BC
[Main] Firebase App Check 디버그 모드 초기화 완료
[AppCheckUtils] App Check 토큰 획득 성공
```

### 3.3 문제 해결
만약 토큰 오류가 발생하면:
1. Firebase Console에서 디버그 토큰이 정확히 등록되었는지 확인
2. 앱 패키지명/번들 ID가 Firebase 프로젝트와 일치하는지 확인
3. 인터넷 연결 상태 확인

## 4. 프로덕션 배포 준비

### 4.1 프로덕션 Provider 설정 ✅ 자동화 완료
**별도 작업 불필요** - 이미 자동으로 설정됩니다:

```dart
// 현재 구현: 빌드 모드에 따라 자동 선택
await FirebaseAppCheck.instance.activate(
  androidProvider: AppCheckConfig.androidProvider, // 자동: debug 또는 playIntegrity
  appleProvider: AppCheckConfig.appleProvider,     // 자동: debug 또는 appAttest
);

// AppCheckConfig 내부 로직:
// - 디버그 빌드: AndroidProvider.debug + AppleProvider.debug
// - 릴리즈 빌드: AndroidProvider.playIntegrity + AppleProvider.appAttest
```

**✅ 현재 상태: 프로덕션 준비 완료** - `kDebugMode`를 사용해서 빌드 타입을 자동 감지합니다.

### 4.2 앱 서명 확인
- Android: Play Console에서 앱 서명 인증서 확인
- iOS: App Store Connect에서 인증서 확인

## 5. 모니터링

### 5.1 Firebase Console에서 확인
- App Check > Overview: 요청 통계 확인
- App Check > Metrics: 성공률 및 오류율 모니터링

### 5.2 앱 로그 모니터링
`AppCheckUtils`를 통해 실시간 상태 확인 가능

## 주의사항
- 디버그 토큰은 개발 환경에서만 사용
- 프로덕션 배포 전 반드시 Provider 변경 필요
- App Check 설정은 모든 Firebase 서비스에 영향을 줄 수 있음
