// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'event_permission.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_EventPermission _$EventPermissionFromJson(Map<String, dynamic> json) =>
    _EventPermission(
      eventId: (json['eventId'] as num).toInt(),
      userId: json['userId'] as String,
      userNickname: json['userNickname'] as String,
      role: $enumDecode(_$UserRoleEnumMap, json['role']),
      permissions:
          (json['permissions'] as List<dynamic>?)
              ?.map((e) => $enumDecode(_$PermissionEnumMap, e))
              .toList() ??
          const [],
      grantedAt: DateTime.parse(json['grantedAt'] as String),
      updatedAt: json['updatedAt'] == null
          ? null
          : DateTime.parse(json['updatedAt'] as String),
      grantedByUserId: json['grantedByUserId'] as String?,
    );

Map<String, dynamic> _$EventPermissionToJson(_EventPermission instance) =>
    <String, dynamic>{
      'eventId': instance.eventId,
      'userId': instance.userId,
      'userNickname': instance.userNickname,
      'role': instance.role,
      'permissions': instance.permissions,
      'grantedAt': instance.grantedAt.toIso8601String(),
      'updatedAt': instance.updatedAt?.toIso8601String(),
      'grantedByUserId': instance.grantedByUserId,
    };

const _$UserRoleEnumMap = {
  UserRole.owner: 'owner',
  UserRole.invited: 'invited',
};

const _$PermissionEnumMap = {
  Permission.read: 'read',
  Permission.download: 'download',
  Permission.prepaymentManage: 'prepaymentManage',
};
