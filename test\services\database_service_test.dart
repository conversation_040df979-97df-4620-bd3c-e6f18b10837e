import 'package:flutter_test/flutter_test.dart';
import 'package:sqflite_common_ffi/sqflite_ffi.dart';
import 'package:parabara/services/database_service.dart';

void main() {
  late Database db;
  late DatabaseService databaseService;

  setUpAll(() {
    sqfliteFfiInit();
    databaseFactory = databaseFactoryFfi;
  });

  setUp(() async {
    db = await databaseFactory.openDatabase(
      inMemoryDatabasePath,
      options: OpenDatabaseOptions(
        version: 1,
        onCreate: (db, version) async {
          // products 테이블
          await db.execute('''
            CREATE TABLE products (
              id INTEGER PRIMARY KEY AUTOINCREMENT,
              name TEXT NOT NULL,
              quantity INTEGER NOT NULL,
              price INTEGER NOT NULL,
              sellerName TEXT,
              imagePath TEXT,
              isActive INTEGER DEFAULT 1,
              isDiscountItem INTEGER DEFAULT 0,
              discountValue INTEGER DEFAULT 0,
              lastServicedDate INTEGER,

            )
          ''');

          // sales 테이블
          await db.execute('''
            CREATE TABLE sales (
              id INTEGER PRIMARY KEY AUTOINCREMENT,
              productId INTEGER NOT NULL,
              quantity INTEGER NOT NULL,
              totalPrice INTEGER NOT NULL,
              saleTimestamp INTEGER NOT NULL,
              name TEXT,
              sellerName TEXT,
              imagePath TEXT
            )
          ''');

          // sales_log 테이블
          await db.execute('''
            CREATE TABLE sales_log (
              id INTEGER PRIMARY KEY AUTOINCREMENT,
              productId INTEGER,
              productName TEXT NOT NULL,
              sellerName TEXT,
              soldPrice INTEGER NOT NULL,
              soldQuantity INTEGER NOT NULL,
              totalAmount INTEGER NOT NULL,
              saleTimestamp INTEGER NOT NULL,
              transactionType TEXT DEFAULT 'SALE',
              batchSaleId TEXT
            )
          ''');

          // sellers 테이블
          await db.execute('''
            CREATE TABLE sellers (
              id INTEGER PRIMARY KEY AUTOINCREMENT,
              name TEXT NOT NULL,
              contact TEXT,
              registrationTimestamp INTEGER NOT NULL
            )
          ''');

          // prepayments 테이블
          await db.execute('''
            CREATE TABLE prepayments (
              id INTEGER PRIMARY KEY AUTOINCREMENT,
              buyerName TEXT NOT NULL,
              buyerContact TEXT,
              amount INTEGER NOT NULL,
              pickupDayOfWeek INTEGER,
              productNameList TEXT,
              memo TEXT,
              registrationDate INTEGER,
              isReceived INTEGER DEFAULT 0,
              registrationActualDayOfWeek INTEGER,
              bankName TEXT,
              email TEXT,
              twitterAccount TEXT,
              registrationTimestamp INTEGER NOT NULL
            )
          ''');

          // service_gifts 테이블
          await db.execute('''
            CREATE TABLE service_gifts (
              id INTEGER PRIMARY KEY AUTOINCREMENT,
              name TEXT NOT NULL,
              price INTEGER NOT NULL,
              registrationTimestamp INTEGER NOT NULL
            )
          ''');

          // 인덱스 생성
          await db.execute('CREATE INDEX index_sales_log_productId ON sales_log(productId)');
          await db.execute('CREATE INDEX index_sales_log_sellerName ON sales_log(sellerName)');
          await db.execute('CREATE INDEX index_sales_log_saleTimestamp ON sales_log(saleTimestamp)');
          await db.execute('CREATE INDEX index_sales_log_transactionType ON sales_log(transactionType)');
          await db.execute('CREATE INDEX index_sales_log_batchSaleId ON sales_log(batchSaleId)');
          await db.execute('CREATE INDEX index_sales_sellerName ON sales(sellerName)');
        },
      ),
    );

    databaseService = TestDatabaseService(db);
  });

  tearDown(() async {
    // 각 테스트 후 테이블 초기화
    await db.execute('DELETE FROM products');
    await db.execute('DELETE FROM sellers');
    await db.execute('DELETE FROM sales');
    await db.execute('DELETE FROM sales_log');
    await db.execute('DELETE FROM prepayments');
    await db.execute('DELETE FROM service_gifts');
  });

  tearDownAll(() async {
    await db.close();
  });

  group('데이터베이스 연결 테스트', () {
    test('데이터베이스 연결 테스트', () async {
      expect(db.isOpen, isTrue);
    });

    test('DatabaseService를 통한 데이터베이스 접근', () async {
      final database = await databaseService.database;
      expect(database.isOpen, isTrue);
    });
  });

  group('테이블 생성 테스트', () {
    test('products 테이블 생성 확인', () async {
      final result = await db.query(
        'sqlite_master',
        where: 'type = ? AND name = ?',
        whereArgs: ['table', 'products'],
      );
      expect(result.length, 1);
    });

    test('sales 테이블 생성 확인', () async {
      final result = await db.query(
        'sqlite_master',
        where: 'type = ? AND name = ?',
        whereArgs: ['table', 'sales'],
      );
      expect(result.length, 1);
    });

    test('prepayments 테이블 생성 확인', () async {
      final result = await db.query(
        'sqlite_master',
        where: 'type = ? AND name = ?',
        whereArgs: ['table', 'prepayments'],
      );
      expect(result.length, 1);
    });

    test('sales_log 테이블 생성 확인', () async {
      final result = await db.query(
        'sqlite_master',
        where: 'type = ? AND name = ?',
        whereArgs: ['table', 'sales_log'],
      );
      expect(result.length, 1);
    });

    test('sellers 테이블 생성 확인', () async {
      final result = await db.query(
        'sqlite_master',
        where: 'type = ? AND name = ?',
        whereArgs: ['table', 'sellers'],
      );
      expect(result.length, 1);
    });

    test('service_gifts 테이블 생성 확인', () async {
      final result = await db.query(
        'sqlite_master',
        where: 'type = ? AND name = ?',
        whereArgs: ['table', 'service_gifts'],
      );
      expect(result.length, 1);
    });
  });

  group('테이블 스키마 검증 테스트', () {
    test('products 테이블 컬럼 확인', () async {
      final result = await db.rawQuery('PRAGMA table_info(products)');

      final columnNames = result.map((row) => row['name'] as String).toList();
      expect(columnNames, contains('id'));
      expect(columnNames, contains('name'));
      expect(columnNames, contains('quantity'));
      expect(columnNames, contains('price'));
      expect(columnNames, contains('imagePath'));
      expect(columnNames, contains('sellerName'));
      expect(columnNames, contains('lastServicedDate'));
      expect(columnNames, contains('isActive'));
      expect(columnNames, contains('isDiscountItem'));
      expect(columnNames, contains('discountValue'));

      expect(columnNames, contains('registrationTimestamp'));
    });

    test('sales_log 테이블 컬럼 확인', () async {
      final result = await db.rawQuery('PRAGMA table_info(sales_log)');

      final columnNames = result.map((row) => row['name'] as String).toList();
      expect(columnNames, contains('id'));
      expect(columnNames, contains('productId'));
      expect(columnNames, contains('productName'));
      expect(columnNames, contains('sellerName'));
      expect(columnNames, contains('soldPrice'));
      expect(columnNames, contains('soldQuantity'));
      expect(columnNames, contains('totalAmount'));
      expect(columnNames, contains('saleTimestamp'));
      expect(columnNames, contains('transactionType'));
      expect(columnNames, contains('batchSaleId'));
    });

    test('prepayments 테이블 컬럼 확인', () async {
      final result = await db.rawQuery('PRAGMA table_info(prepayments)');

      final columnNames = result.map((row) => row['name'] as String).toList();
      expect(columnNames, contains('id'));
      expect(columnNames, contains('buyerName'));
      expect(columnNames, contains('buyerContact'));
      expect(columnNames, contains('amount'));
      expect(columnNames, contains('pickupDayOfWeek'));
      expect(columnNames, contains('productNameList'));
      expect(columnNames, contains('memo'));
      expect(columnNames, contains('registrationDate'));
      expect(columnNames, contains('isReceived'));
      expect(columnNames, contains('registrationActualDayOfWeek'));
      expect(columnNames, contains('bankName'));
      expect(columnNames, contains('email'));
      expect(columnNames, contains('twitterAccount'));
      expect(columnNames, contains('registrationTimestamp'));
    });

    test('service_gifts 테이블 컬럼 확인', () async {
      final result = await db.rawQuery(
        'PRAGMA table_info(service_gifts)',
      );

      final columnNames = result.map((row) => row['name'] as String).toList();
      expect(columnNames, contains('id'));
      expect(columnNames, contains('name'));
      expect(columnNames, contains('price'));
      expect(columnNames, contains('registrationTimestamp'));
    });
  });

  group('인덱스 생성 테스트', () {
    test('sales_log 인덱스 확인', () async {
      final result = await db.rawQuery(
        "SELECT name FROM sqlite_master WHERE type='index' AND tbl_name='sales_log'",
      );

      final indexNames = result.map((row) => row['name'] as String).toList();
      expect(indexNames, contains('index_sales_log_productId'));
      expect(indexNames, contains('index_sales_log_sellerName'));
      expect(indexNames, contains('index_sales_log_saleTimestamp'));
      expect(indexNames, contains('index_sales_log_transactionType'));
      expect(indexNames, contains('index_sales_log_batchSaleId'));
    });

    test('sales 테이블 인덱스 확인', () async {
      final result = await db.rawQuery(
        "SELECT name FROM sqlite_master WHERE type='index' AND tbl_name='sales'",
      );
      final indexNames = result.map((row) => row['name'] as String).toList();
      expect(indexNames, contains('index_sales_sellerName'));
    });
  });

  group('기본 CRUD 작업 테스트', () {
    test('products 테이블 CRUD 테스트', () async {
      // Insert
      final id = await db.insert('products', {
        'name': 'Test Product',
        'quantity': 10,
        'price': 1000,
        'sellerName': 'Test Seller',
        'registrationTimestamp': DateTime.now().millisecondsSinceEpoch,
      });
      expect(id, isPositive);

      // Read
      final products = await db.query('products', where: 'id = ?', whereArgs: [id]);
      expect(products.length, 1);
      expect(products.first['name'], equals('Test Product'));

      // Update
      await db.update(
        'products',
        {'quantity': 20},
        where: 'id = ?',
        whereArgs: [id],
      );

      final updatedProducts = await db.query('products', where: 'id = ?', whereArgs: [id]);
      expect(updatedProducts.first['quantity'], equals(20));

      // Delete
      await db.delete('products', where: 'id = ?', whereArgs: [id]);
      final deletedProducts = await db.query('products', where: 'id = ?', whereArgs: [id]);
      expect(deletedProducts.length, 0);
    });

    test('sales_log 테이블 CRUD 테스트', () async {
      // Insert
      final id = await db.insert('sales_log', {
        'productName': 'Test Product',
        'sellerName': 'Test Seller',
        'soldPrice': 1000,
        'soldQuantity': 1,
        'totalAmount': 1000,
        'saleTimestamp': DateTime.now().millisecondsSinceEpoch,
        'transactionType': 'SALE',
      });
      expect(id, isPositive);

      // Read
      final logs = await db.query('sales_log', where: 'id = ?', whereArgs: [id]);
      expect(logs.length, 1);
      expect(logs.first['productName'], equals('Test Product'));

      // Update
      await db.update(
        'sales_log',
        {'soldQuantity': 2, 'totalAmount': 2000},
        where: 'id = ?',
        whereArgs: [id],
      );

      final updatedLogs = await db.query('sales_log', where: 'id = ?', whereArgs: [id]);
      expect(updatedLogs.first['soldQuantity'], equals(2));
      expect(updatedLogs.first['totalAmount'], equals(2000));

      // Delete
      await db.delete('sales_log', where: 'id = ?', whereArgs: [id]);
      final deletedLogs = await db.query('sales_log', where: 'id = ?', whereArgs: [id]);
      expect(deletedLogs.length, 0);
    });
  });
}

/// 테스트용 DatabaseService 구현
class TestDatabaseService implements DatabaseService {
  final Database _db;

  TestDatabaseService(this._db);

  @override
  Future<Database> get database async => _db;


}
