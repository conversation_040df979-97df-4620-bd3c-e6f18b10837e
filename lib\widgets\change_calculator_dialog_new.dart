import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../utils/currency_utils.dart';
import '../utils/app_colors.dart';

/// 거스름돈 계산 다이얼로그 - 미니멀 디자인
class ChangeCalculatorDialog extends StatefulWidget {
  final int totalAmount;

  const ChangeCalculatorDialog({
    super.key,
    required this.totalAmount,
  });

  static Future<void> show({
    required BuildContext context,
    required int totalAmount,
  }) {
    return showDialog<void>(
      context: context,
      barrierDismissible: false,
      builder: (context) => ChangeCalculatorDialog(totalAmount: totalAmount),
    );
  }

  @override
  State<ChangeCalculatorDialog> createState() => _ChangeCalculatorDialogState();
}

class _ChangeCalculatorDialogState extends State<ChangeCalculatorDialog> {
  final TextEditingController _receivedController = TextEditingController();
  int _receivedAmount = 0;
  int _changeAmount = 0;

  @override
  void initState() {
    super.initState();
    _receivedController.addListener(_calculateChange);
  }

  @override
  void dispose() {
    _receivedController.dispose();
    super.dispose();
  }

  void _calculateChange() {
    final text = _receivedController.text.replaceAll(',', '').replaceAll('₩', '');
    final received = int.tryParse(text) ?? 0;
    setState(() {
      _receivedAmount = received;
      _changeAmount = received - widget.totalAmount;
    });
  }

  void _addAmount(int amount) {
    final currentAmount = _receivedAmount + amount;
    _receivedController.text = CurrencyUtils.formatCurrency(currentAmount).replaceAll('₩', '');
  }

  @override
  Widget build(BuildContext context) {
    final isTablet = MediaQuery.of(context).size.width > 600;
    final screenSize = MediaQuery.of(context).size;
    
    return Dialog(
      backgroundColor: Colors.white,
      surfaceTintColor: Colors.transparent,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      insetPadding: EdgeInsets.all(isTablet ? 24 : 16),
      child: Container(
        width: isTablet ? 480 : screenSize.width * 0.9,
        constraints: BoxConstraints(
          maxHeight: screenSize.height * 0.8,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 헤더 - 미니멀한 디자인
            Container(
              padding: EdgeInsets.all(isTablet ? 24 : 20),
              decoration: const BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(16),
                  topRight: Radius.circular(16),
                ),
                border: Border(
                  bottom: BorderSide(
                    color: Color(0xFFF5F5F5),
                    width: 1,
                  ),
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.calculate_outlined,
                    color: Colors.grey.shade600,
                    size: 24,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      '거스름돈 계산',
                      style: TextStyle(
                        fontFamily: 'Pretendard',
                        fontSize: isTablet ? 20 : 18,
                        fontWeight: FontWeight.w600,
                        color: Colors.grey.shade800,
                      ),
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: Icon(
                      Icons.close,
                      color: Colors.grey.shade500,
                      size: 20,
                    ),
                    style: IconButton.styleFrom(
                      backgroundColor: Colors.grey.shade50,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      padding: const EdgeInsets.all(8),
                    ),
                  ),
                ],
              ),
            ),
            
            // 콘텐츠
            Flexible(
              child: Padding(
                padding: EdgeInsets.all(isTablet ? 24 : 20),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    // 총 결제 금액 카드 - 미니멀 디자인
                    Container(
                      padding: EdgeInsets.all(isTablet ? 20 : 16),
                      decoration: BoxDecoration(
                        color: Colors.grey.shade50,
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: Colors.grey.shade200,
                          width: 1,
                        ),
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            '총 결제 금액',
                            style: TextStyle(
                              fontFamily: 'Pretendard',
                              fontSize: 14,
                              fontWeight: FontWeight.w500,
                              color: Colors.grey.shade600,
                            ),
                          ),
                          Text(
                            CurrencyUtils.formatCurrencyWithWon(widget.totalAmount),
                            style: TextStyle(
                              fontFamily: 'Pretendard',
                              fontSize: isTablet ? 20 : 18,
                              fontWeight: FontWeight.w700,
                              color: Colors.grey.shade800,
                            ),
                          ),
                        ],
                      ),
                    ),
                    
                    const SizedBox(height: 24),
                    
                    // 받은 금액 입력 섹션
                    Text(
                      '받은 금액',
                      style: TextStyle(
                        fontFamily: 'Pretendard',
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                        color: Colors.grey.shade700,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: Colors.grey.shade300,
                          width: 1.5,
                        ),
                        color: Colors.white,
                      ),
                      child: TextField(
                        controller: _receivedController,
                        keyboardType: TextInputType.number,
                        inputFormatters: [
                          FilteringTextInputFormatter.digitsOnly,
                          TextInputFormatter.withFunction((oldValue, newValue) {
                            if (newValue.text.isEmpty) return newValue;
                            final number = int.tryParse(newValue.text) ?? 0;
                            final formatted = CurrencyUtils.formatCurrency(number).replaceAll('₩', '');
                            return TextEditingValue(
                              text: formatted,
                              selection: TextSelection.collapsed(offset: formatted.length),
                            );
                          }),
                        ],
                        decoration: InputDecoration(
                          hintText: '받은 금액을 입력하세요',
                          hintStyle: TextStyle(
                            fontFamily: 'Pretendard',
                            color: Colors.grey.shade400,
                          ),
                          prefixIcon: Container(
                            margin: const EdgeInsets.only(left: 16, right: 8),
                            child: Icon(
                              Icons.attach_money,
                              color: AppColors.primarySeed,
                              size: 20,
                            ),
                          ),
                          prefixIconConstraints: const BoxConstraints(
                            minWidth: 44,
                            minHeight: 20,
                          ),
                          border: InputBorder.none,
                          contentPadding: const EdgeInsets.symmetric(
                            horizontal: 16,
                            vertical: 16,
                          ),
                        ),
                        style: TextStyle(
                          fontFamily: 'Pretendard',
                          fontSize: isTablet ? 18 : 16,
                          fontWeight: FontWeight.w600,
                          color: Colors.grey.shade800,
                        ),
                      ),
                    ),
                    
                    const SizedBox(height: 20),
                    
                    // 빠른 입력 버튼들
                    Text(
                      '빠른 입력',
                      style: TextStyle(
                        fontFamily: 'Pretendard',
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                        color: Colors.grey.shade700,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Wrap(
                      spacing: 8,
                      runSpacing: 8,
                      children: [1000, 5000, 10000, 50000].map((amount) {
                        return Material(
                          color: Colors.transparent,
                          child: InkWell(
                            onTap: () => _addAmount(amount),
                            borderRadius: BorderRadius.circular(8),
                            child: Container(
                              padding: EdgeInsets.symmetric(
                                horizontal: isTablet ? 16 : 12,
                                vertical: isTablet ? 10 : 8,
                              ),
                              decoration: BoxDecoration(
                                color: Colors.grey.shade50,
                                borderRadius: BorderRadius.circular(8),
                                border: Border.all(
                                  color: Colors.grey.shade200,
                                  width: 1,
                                ),
                              ),
                              child: Text(
                                '+${CurrencyUtils.formatCurrencyWithWon(amount)}',
                                style: TextStyle(
                                  fontFamily: 'Pretendard',
                                  fontSize: 12,
                                  fontWeight: FontWeight.w500,
                                  color: Colors.grey.shade700,
                                ),
                              ),
                            ),
                          ),
                        );
                      }).toList(),
                    ),
                    
                    const SizedBox(height: 24),
                    
                    // 거스름돈 결과 카드 - 미니멀 디자인
                    Container(
                      padding: EdgeInsets.all(isTablet ? 20 : 16),
                      decoration: BoxDecoration(
                        color: _changeAmount >= 0 
                            ? Colors.green.shade50
                            : Colors.red.shade50,
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: _changeAmount >= 0 
                              ? Colors.green.shade200
                              : Colors.red.shade200,
                          width: 1.5,
                        ),
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            _changeAmount >= 0 ? '거스름돈' : '부족한 금액',
                            style: TextStyle(
                              fontFamily: 'Pretendard',
                              fontSize: 14,
                              fontWeight: FontWeight.w500,
                              color: _changeAmount >= 0 
                                  ? Colors.green.shade700
                                  : Colors.red.shade700,
                            ),
                          ),
                          Text(
                            CurrencyUtils.formatCurrencyWithWon(_changeAmount.abs()),
                            style: TextStyle(
                              fontFamily: 'Pretendard',
                              fontSize: isTablet ? 20 : 18,
                              fontWeight: FontWeight.w700,
                              color: _changeAmount >= 0 
                                  ? Colors.green.shade700
                                  : Colors.red.shade700,
                            ),
                          ),
                        ],
                      ),
                    ),
                    
                    const SizedBox(height: 24),
                    
                    // 확인 버튼
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: () => Navigator.of(context).pop(),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppColors.primarySeed,
                          foregroundColor: Colors.white,
                          padding: EdgeInsets.symmetric(
                            vertical: isTablet ? 16 : 14,
                          ),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                          elevation: 0,
                        ),
                        child: Text(
                          '확인',
                          style: TextStyle(
                            fontFamily: 'Pretendard',
                            fontSize: isTablet ? 16 : 14,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
