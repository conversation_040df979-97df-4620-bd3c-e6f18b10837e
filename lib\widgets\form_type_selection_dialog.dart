import 'package:flutter/material.dart';
import '../utils/app_colors.dart';

/// 엑셀 폼 타입 선택 다이얼로그
class FormTypeSelectionDialog extends StatelessWidget {
  const FormTypeSelectionDialog({super.key});

  static Future<String?> show(BuildContext context) {
    return showDialog<String>(
      context: context,
      barrierDismissible: false,
      builder: (context) => const FormTypeSelectionDialog(),
    );
  }

  @override
  Widget build(BuildContext context) {
    final isTablet = MediaQuery.of(context).size.width > 600;
    
    return AlertDialog(
      title: Row(
        children: [
          Icon(
            Icons.description,
            color: AppColors.primarySeed,
            size: 24,
          ),
          const SizedBox(width: 8),
          const Text(
            '엑셀 폼 타입 선택',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
      content: SizedBox(
        width: isTablet ? 400 : 300,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '업로드할 엑셀 파일의 위치폼 타입을 선택해주세요.',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey,
              ),
            ),
            const SizedBox(height: 24),
            
            // 슬림폼 선택 카드
            _buildFormTypeCard(
              context: context,
              title: '슬림폼',
              description: '슬림폼 사용자 엑셀 등록',
              icon: Icons.view_list,
              color: Colors.blue,
              onTap: () => Navigator.of(context).pop('slim'),
            ),
            
            const SizedBox(height: 16),
            
            // 페이폼 선택 카드
            _buildFormTypeCard(
              context: context,
              title: '페이폼',
              description: '페이폼 사용자 엑셀 등록',
              icon: Icons.receipt_long,
              color: Colors.green,
              onTap: () => Navigator.of(context).pop('payform'),
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('취소'),
        ),
      ],
    );
  }

  Widget _buildFormTypeCard({
    required BuildContext context,
    required String title,
    required String description,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          border: Border.all(
            color: color.withOpacity(0.3),
            width: 2,
          ),
          borderRadius: BorderRadius.circular(12),
          color: color.withOpacity(0.05),
        ),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: color.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                icon,
                color: color,
                size: 24,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                      color: color,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    description,
                    style: const TextStyle(
                      fontSize: 14,
                      color: Colors.grey,
                    ),
                  ),
                ],
              ),
            ),
            Icon(
              Icons.arrow_forward_ios,
              color: color,
              size: 16,
            ),
          ],
        ),
      ),
    );
  }
}
