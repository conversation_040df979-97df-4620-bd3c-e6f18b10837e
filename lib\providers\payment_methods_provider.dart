import 'dart:async';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

import '../models/payment_method.dart';
import '../utils/logger_utils.dart';

class PaymentMethodsState {
  final List<PaymentMethod> methods;
  final bool isLoading;
  final String? error;

  const PaymentMethodsState({
    this.methods = const [],
    this.isLoading = false,
    this.error,
  });

  PaymentMethodsState copyWith({
    List<PaymentMethod>? methods,
    bool? isLoading,
    String? error,
  }) => PaymentMethodsState(
        methods: methods ?? this.methods,
        isLoading: isLoading ?? this.isLoading,
        error: error,
      );

  /// 활성화된 결제수단만 반환
  List<PaymentMethod> get activeMethods =>
      methods.where((m) => m.isActive).toList();
}

class PaymentMethodsNotifier extends StateNotifier<PaymentMethodsState> {
  static const _tag = 'PaymentMethodsProvider';

  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  StreamSubscription? _authSub;
  StreamSubscription<DocumentSnapshot>? _pmDocSub;

  PaymentMethodsNotifier() : super(const PaymentMethodsState()) {
    _start();
  }

  void _start() {
    // 중복 구독 방지
    if (_authSub != null) {
      LoggerUtils.logInfo('PaymentMethods 이미 초기화됨 - 중복 실행 방지', tag: _tag);
      return;
    }

    // 인증 상태를 감지하여 초기 로드만 수행 (실시간 구독 제거)
    _authSub = _auth.authStateChanges().listen((user) async {
      await _loadInitialData(user);
    });
    // 현재 사용자에 대해서도 즉시 한번 시도
    _loadInitialData(_auth.currentUser);
  }

  /// 초기 데이터 로드 (실시간 구독 없이)
  Future<void> _loadInitialData(User? user) async {
    // 기존 구독 해제
    await _pmDocSub?.cancel();
    _pmDocSub = null;

    if (user == null) {
      LoggerUtils.logDebug('로그인 사용자 없음 - 결제수단 초기화', tag: _tag);
      state = const PaymentMethodsState(methods: []);
      return;
    }

    final docRef = _firestore
        .collection('users')
        .doc(user.uid)
        .collection('settings')
        .doc('payment_methods');

    state = state.copyWith(isLoading: true);

    // 문서가 없으면 기본값으로 생성
    try {
      final snap = await docRef.get();
      if (!snap.exists || (snap.data()?['methods'] == null)) {
        final defaults = _defaultMethods();
        await docRef.set({
          'methods': defaults.map((m) => m.toJson()).toList(),
          'updatedAt': FieldValue.serverTimestamp(),
        }, SetOptions(merge: true));
        state = state.copyWith(methods: defaults, isLoading: false);
      }
    } catch (e) {
      LoggerUtils.logWarning('결제수단 기본 생성 확인 중 오류: $e', tag: _tag);
    }

    // 실시간 구독 대신 한 번만 읽기 (Firestore 읽기 사용량 최적화)
    try {
      final snapshot = await docRef.get();
      if (snapshot.exists && snapshot.data() != null) {
        final data = snapshot.data() as Map<String, dynamic>;
        final list = (data['methods'] as List<dynamic>? ?? [])
            .map((e) => PaymentMethod.fromJson(Map<String, dynamic>.from(e as Map)))
            .toList()
          ..sort((a, b) => a.order.compareTo(b.order));

        // 고정된 3가지 결제수단 보장 및 기존 데이터 호환성 처리
        List<PaymentMethod> normalized = _ensureAllPaymentMethods(list);

        // 다음 경우에 서버에 정규화된 데이터 저장:
        // 1. 결제수단 개수가 3개가 아닌 경우
        // 2. 필수 결제수단이 누락된 경우
        // 3. 기존 데이터에 isActive 필드가 없는 경우 (fromJson에서 기본값 적용됨)
        bool needsUpdate = list.length != 3 ||
                          !_hasAllRequiredMethods(list) ||
                          list.any((m) => !data['methods'].any((json) =>
                            json['id'] == m.id && json.containsKey('isActive')));

        if (needsUpdate) {
          LoggerUtils.logInfo('결제수단 데이터 정규화 필요 - 서버 업데이트', tag: _tag);
          await _save(normalized); // 정규화된 데이터를 서버에 저장
        }

        state = state.copyWith(methods: normalized, isLoading: false, error: null);
      } else {
        // 문서가 없으면 기본값 사용
        state = state.copyWith(methods: _defaultMethods(), isLoading: false, error: null);
      }
    } catch (e) {
      LoggerUtils.logError('결제수단 초기 로드 실패', tag: _tag, error: e);
      state = state.copyWith(methods: _defaultMethods(), isLoading: false, error: e.toString());
    }
  }

  List<PaymentMethod> _defaultMethods() => [
        PaymentMethod(id: 'cash', name: '현금', order: 0, isActive: true),
        PaymentMethod(id: 'transfer', name: '계좌이체', order: 1, isActive: true),
        PaymentMethod(id: 'card', name: '카드', order: 2, isActive: false),
      ];

  /// 모든 필수 결제수단이 있는지 확인
  bool _hasAllRequiredMethods(List<PaymentMethod> methods) {
    final requiredIds = {'cash', 'transfer', 'card'};
    final existingIds = methods.map((m) => m.id).toSet();
    return requiredIds.every((id) => existingIds.contains(id));
  }

  /// 누락된 결제수단을 추가하여 3가지 모두 보장 (기존 데이터 호환성 고려)
  List<PaymentMethod> _ensureAllPaymentMethods(List<PaymentMethod> existing) {
    final defaults = _defaultMethods();
    final existingMap = {for (var m in existing) m.id: m};

    return defaults.map((defaultMethod) {
      final existingMethod = existingMap[defaultMethod.id];
      if (existingMethod != null) {
        // 기존 데이터가 있으면 그대로 사용 (fromJson에서 isActive 기본값 처리됨)
        return existingMethod;
      } else {
        // 누락된 결제수단은 기본값으로 추가
        return defaultMethod;
      }
    }).toList();
  }

  /// 결제수단 활성화/비활성화 토글
  Future<void> toggleMethod(String id) async {
    final currentMethods = state.methods;
    final activeCount = currentMethods.where((m) => m.isActive).length;
    final targetMethod = currentMethods.firstWhere((m) => m.id == id);

    // 활성화된 결제수단이 1개뿐이고 그것을 비활성화하려는 경우 방지
    if (activeCount <= 1 && targetMethod.isActive) return;

    final newList = currentMethods.map((m) =>
      m.id == id ? m.copyWith(isActive: !m.isActive) : m
    ).toList();

    await _save(newList);
  }





  Future<void> reorder(List<PaymentMethod> ordered) async {
    final newList = [
      for (int i = 0; i < ordered.length && i < 3; i++)
        ordered[i].copyWith(order: i)
    ];
    if (newList.isEmpty) return;
    await _save(newList);
  }

  Future<void> _save(List<PaymentMethod> list) async {
    final user = _auth.currentUser;
    if (user == null) return;
    final docRef = _firestore
        .collection('users')
        .doc(user.uid)
        .collection('settings')
        .doc('payment_methods');
    try {
      await docRef.set({
        'methods': list.map((m) => m.toJson()).toList(),
        'updatedAt': FieldValue.serverTimestamp(),
      }, SetOptions(merge: true));

      // 저장 후 다시 로드 (실시간 구독이 없으므로)
      await _loadInitialData(user);
    } catch (e) {
      LoggerUtils.logError('결제수단 저장 실패', tag: _tag, error: e);
      rethrow;
    }
  }

  @override
  void dispose() {
    _pmDocSub?.cancel();
    _authSub?.cancel();
    super.dispose();
  }
}

final paymentMethodsProvider = StateNotifierProvider<PaymentMethodsNotifier, PaymentMethodsState>((ref) {
  final notifier = PaymentMethodsNotifier();
  ref.onDispose(() => notifier.dispose());
  return notifier;
});

