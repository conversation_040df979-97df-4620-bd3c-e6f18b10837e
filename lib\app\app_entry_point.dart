import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:syncfusion_localizations/syncfusion_localizations.dart';
import '../screens/splash/splash_screen.dart';
import '../utils/logger_utils.dart';
import '../screens/onboarding/onboarding_screen.dart';
import '../screens/auth/login_screen.dart';
import '../screens/auth/register_screen.dart';
import '../screens/sync/sync_confirmation_screen.dart';
import 'app_wrapper.dart';
import '../screens/inventory/inventory_screen.dart';
import '../screens/settings/my_page_screen.dart';
import '../screens/product/register_product_screen.dart';
import '../screens/prepayment/register_prepayment_screen.dart';
import '../screens/sale/sale_screen.dart';
import '../screens/checklist/checklist_screen.dart';
import '../screens/sales_log/sales_log_screen.dart';
import '../screens/statistics/statistics_screen.dart';
import '../screens/records_and_statistics/records_and_statistics_screen.dart';
import '../screens/settings/settings_screen.dart';
import '../pages/admin/admin_login_page.dart';
import '../pages/admin/admin_dashboard_page.dart';
import '../providers/data_sync_provider.dart';
import '../providers/unified_workspace_provider.dart';
import '../providers/realtime_sync_provider.dart';
import '../utils/safe_dialog_utils.dart';
import 'app_themes.dart';

/// 앱 진입점: Splash(Firebase 초기화) → 온보딩/로그인 플로우
class AppEntryPoint extends StatefulWidget {
  const AppEntryPoint({super.key});

  @override
  State<AppEntryPoint> createState() => _AppEntryPointState();
}

class _AppEntryPointState extends State<AppEntryPoint> {
  bool _isOnboarded = false;
  bool _isLoading = true;
  String _authMode = 'login'; // 'login' or 'register'
  bool _needsSyncCheck = false; // 동기화 확인이 필요한지 여부
  DateTime? _splashStartTime; // 스플래시 화면 시작 시간
  bool _skipAccountValidation = false; // 로그인 성공 직후 검증 건너뛰기

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
  }

  @override
  void initState() {
    super.initState();
    _splashStartTime = DateTime.now(); // 스플래시 시작 시간 기록
    // SplashScreen 완료 후 온보딩 체크 수행
    _checkOnboardingInBackground();
  }

  /// 백그라운드에서 온보딩 상태 체크 (UI 변경 없이 결과만 저장)
  Future<void> _checkOnboardingInBackground() async {
    try {
      await _checkOnboarding();
    } catch (e) {
      // 오류 발생 시 기본값 설정
      _isOnboarded = false;
      _authMode = 'login';
    }
  }



  /// 스플래시 화면 완료 후 호출되는 콜백
  void _onSplashComplete() {
    _finishSplashScreen(
      isOnboarded: _isOnboarded,
      authMode: _authMode,
      needsSyncCheck: _needsSyncCheck,
    );
  }

  /// 스플래시 화면 최소 시간 보장 후 로딩 완료 처리
  Future<void> _finishSplashScreen({
    bool? isOnboarded,
    String? authMode,
    bool? needsSyncCheck,
  }) async {
    const minSplashDuration = Duration(seconds: 2); // 최소 2초 스플래시 표시

    if (_splashStartTime != null) {
      final elapsed = DateTime.now().difference(_splashStartTime!);
      final remainingTime = minSplashDuration - elapsed;

      if (remainingTime > Duration.zero) {
        // 최소 시간이 지나지 않았으면 추가 대기
        await Future.delayed(remainingTime);
      }
    }

    if (mounted) {
      setState(() {
        if (isOnboarded != null) _isOnboarded = isOnboarded;
        if (authMode != null) _authMode = authMode;
        if (needsSyncCheck != null) _needsSyncCheck = needsSyncCheck;
        _isLoading = false;
      });
    }
  }

  Future<void> _checkOnboarding() async {
    final prefs = await SharedPreferences.getInstance();
    
    // 로그아웃 완료 플래그 확인
    final logoutCompleted = prefs.getBool('logout_completed') ?? false;
    if (logoutCompleted) {
      await prefs.remove('logout_completed');
      LoggerUtils.logInfo('🔄 로그아웃 완료 - 로그인 화면으로 이동', tag: 'AppEntryPoint');
      // Firebase 인증 상태도 확실히 클리어
      await FirebaseAuth.instance.signOut();
      // 로그아웃 상태 저장 (UI 변경 없음)
      _isOnboarded = false;
      _authMode = 'login';
      return;
    }
    
    // 앱 재시작 플래그 확인 (회원탈퇴 후)
    final forceAppRestart = prefs.getBool('force_app_restart') ?? false;
    if (forceAppRestart) {
      await prefs.remove('force_app_restart');
      LoggerUtils.logInfo('🔄 회원탈퇴 후 앱 재시작 - 온보딩 상태 초기화', tag: 'AppEntryPoint');
      // 온보딩 상태 저장 (UI 변경 없음)
      _isOnboarded = false;
      return;
    }
    
    // 계정 삭제 플래그 확인 및 알림 표시
    final accountDeletedByOtherDevice = prefs.getBool('account_deleted_by_other_device') ?? false;
    if (accountDeletedByOtherDevice) {
      await prefs.remove('account_deleted_by_other_device');
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _showAccountDeletedDialog();
      });
    }
    
    // 디버그 모드에서만 추가 검증 로직
    bool forceReset = false;
    bool debugModeReset = false;
    
    assert(() {
      // 디버그 모드에서만 실행되는 코드
      LoggerUtils.logInfo('🔍 디버그 모드: 온보딩 상태 상세 분석 시작', tag: 'AppEntryPoint');
      
      // 1. 기존 강제 리셋 플래그 확인
      forceReset = prefs.getBool('force_onboarding_reset') ?? false;
      if (forceReset) {
        LoggerUtils.logInfo('🔄 강제 온보딩 초기화 플래그 발견', tag: 'AppEntryPoint');
      }
      
      // 2. SharedPreferences 상태 상세 로깅
      final currentOnboardedState = prefs.getBool('isOnboarded');
      final allKeys = prefs.getKeys();
      LoggerUtils.logInfo('📋 SharedPreferences 상태:', tag: 'AppEntryPoint');
      LoggerUtils.logInfo('  - isOnboarded: $currentOnboardedState', tag: 'AppEntryPoint');
      LoggerUtils.logInfo('  - force_onboarding_reset: ${prefs.getBool('force_onboarding_reset')}', tag: 'AppEntryPoint');
      LoggerUtils.logInfo('  - 전체 키 개수: ${allKeys.length}', tag: 'AppEntryPoint');
      
      // 3. Firebase 사용자 상태 확인
      final user = FirebaseAuth.instance.currentUser;
      LoggerUtils.logInfo('🔐 Firebase 사용자 상태: ${user?.email ?? 'null'}', tag: 'AppEntryPoint');
      
      // 4. 디버그 모드에서의 추가 검증 로직 (이메일 인증 대기 상태 고려)
      if (currentOnboardedState == true && user == null) {
        // 이메일 인증 대기 상태인지 확인
        final isEmailVerificationPending = prefs.getBool('email_verification_pending') ?? false;
        
        if (isEmailVerificationPending) {
          LoggerUtils.logInfo('📧 이메일 인증 대기 중 - 온보딩 리셋하지 않음', tag: 'AppEntryPoint');
        } else {
          LoggerUtils.logInfo('⚠️  의심스러운 상태 감지: 온보딩 완료되었지만 Firebase 사용자 없음', tag: 'AppEntryPoint');
          LoggerUtils.logInfo('🔄 디버그 모드: 온보딩 상태 자동 리셋', tag: 'AppEntryPoint');
          debugModeReset = true;
        }
      }
      
      return true;
    }());

    // 리셋 실행
    if (forceReset || debugModeReset) {
      LoggerUtils.logInfo('🔄 온보딩 상태 리셋 실행', tag: 'AppEntryPoint');
      await prefs.remove('isOnboarded');
      await prefs.remove('force_onboarding_reset');
    }

    final isOnboarded = (forceReset || debugModeReset) ? false : (prefs.getBool('isOnboarded') ?? false);
    final user = FirebaseAuth.instance.currentUser;
    
    // 최종 상태 로깅
    LoggerUtils.logInfo('=== 온보딩 상태 확인 결과 ===', tag: 'AppEntryPoint');
    LoggerUtils.logInfo('isOnboarded: $isOnboarded', tag: 'AppEntryPoint');
    LoggerUtils.logInfo('Firebase User: ${user?.email ?? 'null'}', tag: 'AppEntryPoint');
    LoggerUtils.logInfo('Platform: ${Platform.isIOS ? 'iOS' : 'Android'}', tag: 'AppEntryPoint');
    LoggerUtils.logInfo('Force Reset: $forceReset', tag: 'AppEntryPoint');
    LoggerUtils.logInfo('Debug Mode Reset: $debugModeReset', tag: 'AppEntryPoint');
    LoggerUtils.logInfo('Final Decision: ${!isOnboarded ? "온보딩 화면 표시" : "로그인/메인 화면 표시"}', tag: 'AppEntryPoint');
    
    // 온보딩 상태 저장 (UI 변경 없음)
    _isOnboarded = isOnboarded;
  }

  void _completeOnboarding() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('isOnboarded', true);
    setState(() {
      _isOnboarded = true;
    });
  }

  Future<bool> _validateUserAccount() async {
    final user = FirebaseAuth.instance.currentUser;
    if (user == null) return true; // 로그인되지 않은 상태는 정상
    
    try {
      LoggerUtils.logInfo('사용자 계정 유효성 검증 시작: ${user.email}', tag: 'AppEntryPoint');
      
      // ProviderScope.containerOf를 사용하여 DataSyncService에 접근
      final container = ProviderScope.containerOf(context, listen: false);
      final dataSyncService = container.read(dataSyncServiceProvider);
      
      final isValid = await dataSyncService.validateUserAccount();
      
      if (!isValid) {
        LoggerUtils.logWarning('서버에 계정이 존재하지 않음 - 로컬 인증 상태 클리어', tag: 'AppEntryPoint');
        
        // 로컬 인증 상태 클리어
        await FirebaseAuth.instance.signOut();
        
        // SharedPreferences 클리어 (온보딩 상태는 유지)
        final prefs = await SharedPreferences.getInstance();
        await prefs.remove('email_verification_pending');
        
        // 계정 삭제 알림 표시
        _showAccountDeletedByServerDialog();
        
        return false;
      }
      
      LoggerUtils.logInfo('사용자 계정 유효성 검증 완료: 정상', tag: 'AppEntryPoint');
      return true;
      
    } catch (e) {
      LoggerUtils.logError('사용자 계정 유효성 검증 실패', tag: 'AppEntryPoint', error: e);
      // 검증 실패 시에는 일시적 문제로 간주하여 계속 진행
      return true;
    } finally {
      // 계정 검증 완료
    }
  }

  void _showAccountDeletedByServerDialog() {
    if (!mounted) return;
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Text('계정을 찾을 수 없습니다'),
        content: const Text(
          '서버에서 계정 정보를 찾을 수 없습니다.\n'
          '계정이 삭제되었거나 일시적인 문제일 수 있습니다.\n'
          '로그인 화면으로 이동합니다.'
        ),
        actions: [
          ElevatedButton(
            onPressed: () {
              SafeDialogUtils.safePopDialog(context);
              // 로그인 화면으로 이동
              setState(() {
                _authMode = 'login';
              });
            },
            child: const Text('확인'),
          ),
        ],
      ),
    );
  }

  void _switchAuthMode() {
    setState(() {
      _authMode = _authMode == 'login' ? 'register' : 'login';
    });
  }

  /// 계정 삭제 알림 다이얼로그 표시
  void _showAccountDeletedDialog() {
    if (!mounted) return;

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Text('계정이 삭제되었습니다'),
        content: const Text(
          '다른 기기에서 회원탈퇴가 진행되어 계정이 삭제되었습니다.\n'
          '로컬 데이터가 모두 삭제되었으며, 로그인 화면으로 이동합니다.'
        ),
        actions: [
          ElevatedButton(
            onPressed: () {
              SafeDialogUtils.safePopDialog(context);
              // 온보딩 상태를 false로 설정하여 로그인 화면으로 이동
              setState(() {
                _isOnboarded = true; // 온보딩은 완료된 상태로 유지
              });
            },
            child: const Text('확인'),
          ),
        ],
      ),
    );
  }

  void _onLoginSuccess({required bool hasServerData}) async {
    try {
      final container = ProviderScope.containerOf(context, listen: false);

      // SharedPreferences 정리 (일회성 플래그 제거)
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('has_server_data_on_login');

      LoggerUtils.logInfo('로그인 성공 - 서버 데이터 존재 여부: $hasServerData', tag: 'AppEntryPoint');
      LoggerUtils.logInfo('_needsSyncCheck 설정: $hasServerData', tag: 'AppEntryPoint');

      // 라우팅 관련 플래그를 즉시 설정하여 중간 AppWrapper 깜빡임 방지
      if (!mounted) return;
      setState(() {
        _skipAccountValidation = true;   // 로그인 직후 검증 건너뛰기
        _needsSyncCheck = hasServerData; // 동기화 페이지 여부 즉시 결정
      });
      
      // 3) 부수 작업은 백그라운드에서 수행 (라우팅에 영향 없음)
      if (hasServerData) {
        // 워크스페이스 정보만 선로드 (실패해도 라우팅에는 영향 없음)
        Future(() async {
          try {
            await container.read(unifiedWorkspaceProvider.notifier).refresh();
            LoggerUtils.logInfo('워크스페이스 정보 로드 완료', tag: 'AppEntryPoint');
          } catch (e) {
            LoggerUtils.logError('워크스페이스 정보 로드 실패', tag: 'AppEntryPoint', error: e);
          }
        });
      }
      
      // 실시간 동기화 서비스 준비 (실패 무시)
      Future(() {
        try {
          container.read(realtimeSyncServiceProvider);
          LoggerUtils.logInfo('실시간 동기화 서비스 v2.0.0 준비 완료', tag: 'AppEntryPoint');
        } catch (e) {
          LoggerUtils.logError('실시간 동기화 서비스 접근 실패', tag: 'AppEntryPoint', error: e);
        }
      });
    } catch (e) {
      LoggerUtils.logError('서버 데이터 확인 실패', tag: 'AppEntryPoint', error: e);
      if (!mounted) return;
      setState(() {
        _skipAccountValidation = true; // 그래도 로그인 직후 플로우는 진행
        _needsSyncCheck = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return MaterialApp(
        debugShowCheckedModeBanner: false,
        home: SplashScreen(
          onInitializationComplete: _onSplashComplete,
        ),
      );
    }
    return MaterialApp(
      debugShowCheckedModeBanner: false,
      title: '바라 부스 매니저',
      theme: _buildLightTheme(),
      darkTheme: _buildDarkTheme(),
      locale: const Locale('ko', 'KR'),
      localizationsDelegates: const [
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
        SfGlobalLocalizations.delegate,
      ],
      supportedLocales: const [
        Locale('ko', 'KR'),
        Locale('en', 'US'),
      ],
      routes: {
        '/my_page': (context) => const MyPageScreen(),
        '/inventory': (context) => const AppWrapper(child: InventoryScreen()),
        '/register_product': (context) => const AppWrapper(child: RegisterProductScreen()),
        '/register_prepayment': (context) => const AppWrapper(child: RegisterPrepaymentScreen()),
        '/sale': (context) => const AppWrapper(child: SaleScreen()),
        '/checklist': (context) => const ChecklistScreen(),
        '/sales_log': (context) => const AppWrapper(child: SalesLogScreen()),
        '/statistics': (context) => const AppWrapper(child: StatisticsScreen()),
        '/records_and_statistics': (context) => const AppWrapper(child: RecordsAndStatisticsScreen()),
        '/settings': (context) => const AppWrapper(child: SettingsScreen()),
        '/para@admin': (context) => const AdminLoginPage(),
        '/para@admin/dashboard': (context) => const AdminDashboardPage(),
      },
      home: FutureBuilder<Widget>(
        future: _buildHome(),
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const SplashScreen();
          }
          return snapshot.data ?? OnboardingScreen(onStart: _completeOnboarding);
        },
      ),
    );
  }

  Future<Widget> _buildHome() async {
    try {
      final user = FirebaseAuth.instance.currentUser;

      LoggerUtils.logInfo('_isOnboarded: $_isOnboarded', tag: 'AppEntryPoint');
      LoggerUtils.logInfo('user: ${user?.email ?? 'null'}', tag: 'AppEntryPoint');
      LoggerUtils.logInfo('_needsSyncCheck: $_needsSyncCheck', tag: 'AppEntryPoint');
      LoggerUtils.logInfo('_skipAccountValidation: $_skipAccountValidation', tag: 'AppEntryPoint');

      if (!_isOnboarded) {
        LoggerUtils.logInfo('→ OnboardingScreen으로 이동', tag: 'AppEntryPoint');
        return OnboardingScreen(onStart: _completeOnboarding);
      } else if (user == null) {
        LoggerUtils.logInfo('→ Auth Screen으로 이동 (mode: $_authMode)', tag: 'AppEntryPoint');
        if (_authMode == 'login') {
          return LoginScreen(
            onLoginSuccess: (hasServerData) => _onLoginSuccess(hasServerData: hasServerData),
            onRegisterRequested: _switchAuthMode,
          );
        } else {
          return RegisterScreen(
            onRegisterSuccess: _switchAuthMode,
            onLoginRequested: _switchAuthMode,
          );
        }
      } else {
        // 로그인된 사용자가 있는 경우

        // 로그인 성공 직후이거나 이미 검증 완료된 경우 검증 건너뛰기
        if (_skipAccountValidation) {
          LoggerUtils.logInfo('로그인 성공 직후 - 계정 검증 건너뛰기', tag: 'AppEntryPoint');
          
          // 검증 건너뛰기 플래그 초기화 (다음 앱 시작 시를 위해)
          _skipAccountValidation = false;
          
          // 기존 로직 실행
          if (_needsSyncCheck) {
            return SyncConfirmationScreen(
              onSyncComplete: () {
                if (mounted) {
                  setState(() {
                    _needsSyncCheck = false;
                  });
                }
              },
            );
          } else {
            LoggerUtils.logInfo('→ AppWrapper로 이동', tag: 'AppEntryPoint');
            return const AppWrapper(child: InventoryScreen());
          }
        }
        
        // 앱 시작 시 계정 유효성 검증 (동기적으로 수행하여 확실한 검증)
        final isAccountValid = await _validateUserAccount();
        if (!isAccountValid && mounted) {
          // 계정이 유효하지 않으면 로그인 화면으로 이동
          setState(() {
            _isOnboarded = false;
            _authMode = 'login';
          });
          return OnboardingScreen(onStart: _completeOnboarding);
        }

        // 계정 검증 통과 후 메인 화면으로 진행
        if (_needsSyncCheck) {
          // 서버에 데이터가 있어서 동기화 확인이 필요한 경우
          return SyncConfirmationScreen(
            onSyncComplete: () {
              if (mounted) {
                setState(() {
                  _needsSyncCheck = false;
                });
              }
            },
          );
        } else {
          // 일반적인 메인 화면
          LoggerUtils.logInfo('→ AppWrapper로 이동', tag: 'AppEntryPoint');
          return const AppWrapper(child: InventoryScreen());
        }
      }
    } catch (e) {
      // Firebase 관련 오류 시 온보딩 화면으로 이동
      LoggerUtils.logError('Firebase 인증 확인 실패', tag: 'AppEntryPoint', error: e);
      if (!_isOnboarded) {
        return OnboardingScreen(onStart: _completeOnboarding);
      } else {
        return const AppWrapper(child: InventoryScreen());
      }
    }
  }
}

ThemeData _buildLightTheme() {
  return AppThemes.light();
}

ThemeData _buildDarkTheme() {
  return AppThemes.dark();
}
