// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'checklist_template.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$ChecklistTemplate {

 int? get id; String get title; int get order; bool get isActive; DateTime? get createdAt; DateTime? get updatedAt;// 실시간 동기화 메타데이터
 SyncMetadata? get syncMetadata;
/// Create a copy of ChecklistTemplate
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ChecklistTemplateCopyWith<ChecklistTemplate> get copyWith => _$ChecklistTemplateCopyWithImpl<ChecklistTemplate>(this as ChecklistTemplate, _$identity);

  /// Serializes this ChecklistTemplate to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ChecklistTemplate&&(identical(other.id, id) || other.id == id)&&(identical(other.title, title) || other.title == title)&&(identical(other.order, order) || other.order == order)&&(identical(other.isActive, isActive) || other.isActive == isActive)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt)&&(identical(other.syncMetadata, syncMetadata) || other.syncMetadata == syncMetadata));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,title,order,isActive,createdAt,updatedAt,syncMetadata);

@override
String toString() {
  return 'ChecklistTemplate(id: $id, title: $title, order: $order, isActive: $isActive, createdAt: $createdAt, updatedAt: $updatedAt, syncMetadata: $syncMetadata)';
}


}

/// @nodoc
abstract mixin class $ChecklistTemplateCopyWith<$Res>  {
  factory $ChecklistTemplateCopyWith(ChecklistTemplate value, $Res Function(ChecklistTemplate) _then) = _$ChecklistTemplateCopyWithImpl;
@useResult
$Res call({
 int? id, String title, int order, bool isActive, DateTime? createdAt, DateTime? updatedAt, SyncMetadata? syncMetadata
});


$SyncMetadataCopyWith<$Res>? get syncMetadata;

}
/// @nodoc
class _$ChecklistTemplateCopyWithImpl<$Res>
    implements $ChecklistTemplateCopyWith<$Res> {
  _$ChecklistTemplateCopyWithImpl(this._self, this._then);

  final ChecklistTemplate _self;
  final $Res Function(ChecklistTemplate) _then;

/// Create a copy of ChecklistTemplate
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = freezed,Object? title = null,Object? order = null,Object? isActive = null,Object? createdAt = freezed,Object? updatedAt = freezed,Object? syncMetadata = freezed,}) {
  return _then(_self.copyWith(
id: freezed == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int?,title: null == title ? _self.title : title // ignore: cast_nullable_to_non_nullable
as String,order: null == order ? _self.order : order // ignore: cast_nullable_to_non_nullable
as int,isActive: null == isActive ? _self.isActive : isActive // ignore: cast_nullable_to_non_nullable
as bool,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,syncMetadata: freezed == syncMetadata ? _self.syncMetadata : syncMetadata // ignore: cast_nullable_to_non_nullable
as SyncMetadata?,
  ));
}
/// Create a copy of ChecklistTemplate
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$SyncMetadataCopyWith<$Res>? get syncMetadata {
    if (_self.syncMetadata == null) {
    return null;
  }

  return $SyncMetadataCopyWith<$Res>(_self.syncMetadata!, (value) {
    return _then(_self.copyWith(syncMetadata: value));
  });
}
}


/// Adds pattern-matching-related methods to [ChecklistTemplate].
extension ChecklistTemplatePatterns on ChecklistTemplate {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _ChecklistTemplate value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _ChecklistTemplate() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _ChecklistTemplate value)  $default,){
final _that = this;
switch (_that) {
case _ChecklistTemplate():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _ChecklistTemplate value)?  $default,){
final _that = this;
switch (_that) {
case _ChecklistTemplate() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( int? id,  String title,  int order,  bool isActive,  DateTime? createdAt,  DateTime? updatedAt,  SyncMetadata? syncMetadata)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _ChecklistTemplate() when $default != null:
return $default(_that.id,_that.title,_that.order,_that.isActive,_that.createdAt,_that.updatedAt,_that.syncMetadata);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( int? id,  String title,  int order,  bool isActive,  DateTime? createdAt,  DateTime? updatedAt,  SyncMetadata? syncMetadata)  $default,) {final _that = this;
switch (_that) {
case _ChecklistTemplate():
return $default(_that.id,_that.title,_that.order,_that.isActive,_that.createdAt,_that.updatedAt,_that.syncMetadata);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( int? id,  String title,  int order,  bool isActive,  DateTime? createdAt,  DateTime? updatedAt,  SyncMetadata? syncMetadata)?  $default,) {final _that = this;
switch (_that) {
case _ChecklistTemplate() when $default != null:
return $default(_that.id,_that.title,_that.order,_that.isActive,_that.createdAt,_that.updatedAt,_that.syncMetadata);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _ChecklistTemplate implements ChecklistTemplate {
  const _ChecklistTemplate({this.id, required this.title, this.order = 0, this.isActive = true, this.createdAt, this.updatedAt, this.syncMetadata});
  factory _ChecklistTemplate.fromJson(Map<String, dynamic> json) => _$ChecklistTemplateFromJson(json);

@override final  int? id;
@override final  String title;
@override@JsonKey() final  int order;
@override@JsonKey() final  bool isActive;
@override final  DateTime? createdAt;
@override final  DateTime? updatedAt;
// 실시간 동기화 메타데이터
@override final  SyncMetadata? syncMetadata;

/// Create a copy of ChecklistTemplate
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ChecklistTemplateCopyWith<_ChecklistTemplate> get copyWith => __$ChecklistTemplateCopyWithImpl<_ChecklistTemplate>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$ChecklistTemplateToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ChecklistTemplate&&(identical(other.id, id) || other.id == id)&&(identical(other.title, title) || other.title == title)&&(identical(other.order, order) || other.order == order)&&(identical(other.isActive, isActive) || other.isActive == isActive)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt)&&(identical(other.syncMetadata, syncMetadata) || other.syncMetadata == syncMetadata));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,title,order,isActive,createdAt,updatedAt,syncMetadata);

@override
String toString() {
  return 'ChecklistTemplate(id: $id, title: $title, order: $order, isActive: $isActive, createdAt: $createdAt, updatedAt: $updatedAt, syncMetadata: $syncMetadata)';
}


}

/// @nodoc
abstract mixin class _$ChecklistTemplateCopyWith<$Res> implements $ChecklistTemplateCopyWith<$Res> {
  factory _$ChecklistTemplateCopyWith(_ChecklistTemplate value, $Res Function(_ChecklistTemplate) _then) = __$ChecklistTemplateCopyWithImpl;
@override @useResult
$Res call({
 int? id, String title, int order, bool isActive, DateTime? createdAt, DateTime? updatedAt, SyncMetadata? syncMetadata
});


@override $SyncMetadataCopyWith<$Res>? get syncMetadata;

}
/// @nodoc
class __$ChecklistTemplateCopyWithImpl<$Res>
    implements _$ChecklistTemplateCopyWith<$Res> {
  __$ChecklistTemplateCopyWithImpl(this._self, this._then);

  final _ChecklistTemplate _self;
  final $Res Function(_ChecklistTemplate) _then;

/// Create a copy of ChecklistTemplate
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = freezed,Object? title = null,Object? order = null,Object? isActive = null,Object? createdAt = freezed,Object? updatedAt = freezed,Object? syncMetadata = freezed,}) {
  return _then(_ChecklistTemplate(
id: freezed == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int?,title: null == title ? _self.title : title // ignore: cast_nullable_to_non_nullable
as String,order: null == order ? _self.order : order // ignore: cast_nullable_to_non_nullable
as int,isActive: null == isActive ? _self.isActive : isActive // ignore: cast_nullable_to_non_nullable
as bool,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,syncMetadata: freezed == syncMetadata ? _self.syncMetadata : syncMetadata // ignore: cast_nullable_to_non_nullable
as SyncMetadata?,
  ));
}

/// Create a copy of ChecklistTemplate
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$SyncMetadataCopyWith<$Res>? get syncMetadata {
    if (_self.syncMetadata == null) {
    return null;
  }

  return $SyncMetadataCopyWith<$Res>(_self.syncMetadata!, (value) {
    return _then(_self.copyWith(syncMetadata: value));
  });
}
}

// dart format on
