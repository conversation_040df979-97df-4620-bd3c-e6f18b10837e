import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../models/sale.dart';
import '../services/database_service.dart';
import '../utils/logger_utils.dart';
import '../utils/network_status.dart';
import '../utils/offline_task.dart';

/// 판매 데이터베이스 접근을 위한 Repository Provider입니다.
final saleRepositoryProvider = Provider<SaleRepository>((ref) {
  final databaseService = ref.watch(databaseServiceProvider);
  return SaleRepository(database: databaseService);
});

/// 판매 데이터베이스 접근을 위한 Repository 클래스입니다.
class SaleRepository {
  final DatabaseService database;
  static const String _tag = 'SaleRepository';
  static const String salesTable = 'sales';

  SaleRepository({required this.database});

  Future<void> insertSale(Sale sale) async {
    LoggerUtils.methodStart('insertSale', tag: _tag);
    final db = await database.database;
    await db.insert(salesTable, sale.toMap());
    if (!NetworkStatusUtil.isOnline) {
      final task = OfflineTask(
        id: DateTime.now().microsecondsSinceEpoch.toString(),
        type: OfflineTaskType.insert,
        table: salesTable,
        data: sale.toMap(),
        timestamp: DateTime.now(),
      );
      // 타입 캐스팅 대신 안전한 방법 사용
      if (database is DatabaseServiceImpl) {
        await (database as DatabaseServiceImpl).addOfflineTask(task.toMap());
      } else {
        LoggerUtils.logError(
          'Offline task cannot be added: DatabaseService is not DatabaseServiceImpl',
          tag: _tag,
        );
        throw Exception('Failed to add offline task: Incompatible database service type');
      }
    }
    LoggerUtils.methodEnd('insertSale', tag: _tag);
  }

  Future<void> updateSale(Sale sale) async {
    LoggerUtils.methodStart('updateSale', tag: _tag);
    final db = await database.database;
    await db.update(
      salesTable,
      sale.toMap(),
      where: 'id = ?',
      whereArgs: [sale.id],
    );
    if (!NetworkStatusUtil.isOnline) {
      final task = OfflineTask(
        id: DateTime.now().microsecondsSinceEpoch.toString(),
        type: OfflineTaskType.update,
        table: salesTable,
        data: sale.toMap(),
        timestamp: DateTime.now(),
      );
      await (database as DatabaseServiceImpl).addOfflineTask(task.toMap());
    }
    LoggerUtils.methodEnd('updateSale', tag: _tag);
  }

  /// [sale]: 삭제할 Sale 객체
  /// 반환값: 삭제된 row 개수(오프라인 큐 저장 시에도 row 개수 반환)
  /// 예외: DB 오류/네트워크 상태 등
  Future<int> deleteSale(Sale sale) async {
    final db = await database.database;
    if (sale.id == null) {
      throw Exception('판매 ID가 null입니다. 삭제할 수 없습니다.');
    }
    // 1. 항상 로컬 DB에서 즉시 삭제
    final deletedRows = await db.delete(
      salesTable,
      where: 'id = ?',
      whereArgs: [sale.id!],
    );
    // 2. 오프라인일 때는 동기화 큐에도 기록
    if (!NetworkStatusUtil.isOnline) {
      final task = OfflineTask(
        id: DateTime.now().microsecondsSinceEpoch.toString(),
        type: OfflineTaskType.delete,
        table: salesTable,
        data: sale.toMap(),
        timestamp: DateTime.now(),
      );
      await (database as DatabaseServiceImpl).addOfflineTask(task.toMap());
    }
    LoggerUtils.logDebug('[삭제] 판매 id=${sale.id}, deletedRows=$deletedRows', tag: 'SaleRepository');
    return deletedRows;
  }

  Future<List<Sale>> getAllSales() async {
    LoggerUtils.methodStart('getAllSales', tag: _tag);
    final db = await database.database;
    final List<Map<String, dynamic>> maps = await db.query('sales');
    LoggerUtils.methodEnd('getAllSales', tag: _tag);
    return List.generate(maps.length, (i) => Sale.fromMap(maps[i]));
  }

  Future<Sale?> getSaleById(int id) async {
    LoggerUtils.methodStart('getSaleById', tag: _tag);
    final db = await database.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'sales',
      where: 'id = ?',
      whereArgs: [id],
    );
    LoggerUtils.methodEnd('getSaleById', tag: _tag);
    if (maps.isEmpty) return null;
    return Sale.fromMap(maps.first);
  }
}
