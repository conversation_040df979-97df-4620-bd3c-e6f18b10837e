import 'package:flutter/material.dart';
import '../utils/dialog_theme.dart' as custom_dialog;

/// 사용자 확인 다이얼로그
class ConfirmationDialog extends StatelessWidget {
  final String title;
  final String? message;
  final Widget? content;
  final String? confirmLabel;
  final String? cancelLabel;
  final VoidCallback? onConfirm;
  final VoidCallback? onCancel;
  final bool isDestructive;
  final IconData? icon;

  const ConfirmationDialog({
    super.key,
    required this.title,
    this.message,
    this.content,
    this.confirmLabel,
    this.cancelLabel,
    this.onConfirm,
    this.onCancel,
    this.isDestructive = false,
    this.icon,
  });

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isTablet = screenWidth > 600;

    // 삭제 다이얼로그인 경우 더 넓게 설정
    final dialogWidth = isDestructive ? (isTablet ? 500.0 : 420.0) : null;

    return custom_dialog.DialogTheme.buildResponsiveDialog(
      isCompact: true,
      width: dialogWidth,
      child: Padding(
        padding: custom_dialog.DialogTheme.getCompactDialogPadding(isTablet),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 제목 - 중앙정렬 (UI 열 수 설정과 동일한 스타일)
            Center(
              child: Text(
                title,
                style: custom_dialog.DialogTheme.titleStyle.copyWith(
                  fontSize: isTablet ? 20.0 : 18.0,
                  fontFamily: 'Pretendard',
                ),
              ),
            ),

            SizedBox(height: custom_dialog.DialogTheme.getCompactSectionSpacing(isTablet)),

            // 메시지 - 중앙정렬 (UI 열 수 설정과 동일한 스타일)
            if (content != null)
              content!
            else if (message != null)
              Center(
                child: Text(
                  message!,
                  style: custom_dialog.DialogTheme.contentStyle.copyWith(
                    fontSize: isTablet ? 16.0 : 14.0,
                    fontFamily: 'Pretendard',
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            SizedBox(height: custom_dialog.DialogTheme.getCompactSectionSpacing(isTablet) * 1.5),

            // 모던 버튼들
            Row(
              children: [
                // 취소 버튼 (Secondary Style)
                Expanded(
                  child: custom_dialog.DialogTheme.buildModernButton(
                    text: cancelLabel ?? '취소',
                    onPressed: () {
                      Navigator.of(context).pop(false);
                      onCancel?.call();
                    },
                    isTablet: isTablet,
                    isPrimary: false,
                  ),
                ),
                const SizedBox(width: 16.0),

                // 확인 버튼 (Primary 또는 Destructive Style)
                Expanded(
                  child: custom_dialog.DialogTheme.buildModernButton(
                    text: confirmLabel ?? '확인',
                    onPressed: () {
                      Navigator.of(context).pop(true);
                      onConfirm?.call();
                    },
                    isTablet: isTablet,
                    isPrimary: !isDestructive,
                    isDestructive: isDestructive,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// 일반 확인 다이얼로그 표시
  static Future<bool?> show({
    required BuildContext context,
    required String title,
    String? message,
    Widget? content,
    String? confirmLabel,
    String? cancelLabel,
    VoidCallback? onConfirm,
    VoidCallback? onCancel,
    IconData? icon,
  }) {
    return showDialog<bool>(
      context: context,
      builder: (context) => ConfirmationDialog(
        title: title,
        message: message,
        content: content,
        confirmLabel: confirmLabel,
        cancelLabel: cancelLabel,
        onConfirm: onConfirm,
        onCancel: onCancel,
        icon: icon,
      ),
    );
  }

  /// 삭제 확인 다이얼로그 표시
  static Future<bool?> showDelete({
    required BuildContext context,
    required String title,
    String? message,
    Widget? content,
    String? confirmLabel,
    String? cancelLabel,
    VoidCallback? onConfirm,
    VoidCallback? onCancel,
  }) {
    return showDialog<bool>(
      context: context,
      builder: (context) => ConfirmationDialog(
        title: title,
        message: message,
        content: content,
        confirmLabel: confirmLabel ?? '삭제',
        cancelLabel: cancelLabel,
        onConfirm: onConfirm,
        onCancel: onCancel,
        isDestructive: true,
        icon: Icons.delete_forever,
      ),
    );
  }
}

/// 사용자 확인 바텀 시트
class ConfirmationBottomSheet extends StatelessWidget {
  final String title;
  final String message;
  final String? confirmLabel;
  final String? cancelLabel;
  final VoidCallback? onConfirm;
  final VoidCallback? onCancel;
  final bool isDestructive;
  final IconData? icon;

  const ConfirmationBottomSheet({
    super.key,
    required this.title,
    required this.message,
    this.confirmLabel,
    this.cancelLabel,
    this.onConfirm,
    this.onCancel,
    this.isDestructive = false,
    this.icon,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      padding: EdgeInsets.only(
        top: 24,
        left: 24,
        right: 24,
        bottom: 24 + MediaQuery.of(context).viewPadding.bottom,
      ),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface.withValues(alpha: 0.1),
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (icon != null) ...[
            Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                color:
                    (isDestructive
                            ? theme.colorScheme.error
                            : theme.colorScheme.primary)
                        .withValues(alpha: 0.1),
                shape: BoxShape.circle,
              ),
              child: Icon(
                icon,
                color: isDestructive
                    ? theme.colorScheme.error
                    : theme.colorScheme.primary,
                size: 24,
              ),
            ),
            const SizedBox(height: 16),
          ],
          Text(
            title,
            style: theme.textTheme.titleLarge,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          Text(
            message,
            style: theme.textTheme.bodyMedium,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          Row(
            children: [
              Expanded(
                child: OutlinedButton(
                  onPressed: () {
                    Navigator.of(context).pop(false);
                    onCancel?.call();
                  },
                  child: Text(cancelLabel ?? '취소'),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: FilledButton(
                  onPressed: () {
                    Navigator.of(context).pop(true);
                    onConfirm?.call();
                  },
                  style: isDestructive
                      ? FilledButton.styleFrom(
                          backgroundColor: theme.colorScheme.error,
                          foregroundColor: theme.colorScheme.onError,
                        )
                      : null,
                  child: Text(confirmLabel ?? '확인'),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 일반 확인 바텀 시트 표시
  static Future<bool?> show({
    required BuildContext context,
    required String title,
    required String message,
    String? confirmLabel,
    String? cancelLabel,
    VoidCallback? onConfirm,
    VoidCallback? onCancel,
    IconData? icon,
  }) {
    return showModalBottomSheet<bool>(
      context: context,
      builder: (context) => ConfirmationBottomSheet(
        title: title,
        message: message,
        confirmLabel: confirmLabel,
        cancelLabel: cancelLabel,
        onConfirm: onConfirm,
        onCancel: onCancel,
        icon: icon,
      ),
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
    );
  }

  /// 삭제 확인 바텀 시트 표시
  static Future<bool?> showDelete({
    required BuildContext context,
    required String title,
    required String message,
    String? confirmLabel,
    String? cancelLabel,
    VoidCallback? onConfirm,
    VoidCallback? onCancel,
  }) {
    return showModalBottomSheet<bool>(
      context: context,
      builder: (context) => ConfirmationBottomSheet(
        title: title,
        message: message,
        confirmLabel: confirmLabel ?? '삭제',
        cancelLabel: cancelLabel,
        onConfirm: onConfirm,
        onCancel: onCancel,
        isDestructive: true,
        icon: Icons.delete_forever,
      ),
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
    );
  }
}
