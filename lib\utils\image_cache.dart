import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_cache_manager/flutter_cache_manager.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'dart:io';
import 'dart:typed_data';
import 'package:image/image.dart' as img;
import 'logger_utils.dart';
import 'image_sync_utils.dart';

/// 통합 이미지 캐시 유틸리티
/// 
/// 기존 ImageCacheManager + ImageStateCache 통합
/// - 이미지 캐싱 관리 (ImageCacheManager)
/// - 이미지 상태 캐싱 (ImageStateCache)

/// 이미지 캐시 관리자
/// 
/// 앱의 이미지 캐싱을 최적화하여 메모리 사용량을 줄이고 성능을 향상시킵니다.
class ImageCacheManager {
  static const String _tag = 'ImageCacheManager';
  
  // 싱글톤 인스턴스
  static final ImageCacheManager _instance = ImageCacheManager._internal();
  factory ImageCacheManager() => _instance;
  ImageCacheManager._internal();

  // 커스텀 캐시 매니저 (판매 현장 최적화)
  static final CacheManager _cacheManager = CacheManager(
    Config(
      'sales_optimized_image_cache',
      stalePeriod: const Duration(days: 7), // 7일 후 만료 (판매 기간 고려)
      maxNrOfCacheObjects: 500, // 최대 500개 캐시 객체 (300+ 상품 지원)
      repo: JsonCacheInfoRepository(databaseName: 'sales_optimized_image_cache'),
      fileService: HttpFileService(),
    ),
  );

  /// 이미지 캐시 초기화 및 최적화 설정 (판매 현장 최적화)
  static void initialize() {
    try {
      // Flutter 이미지 캐시 설정 최적화
      final imageCache = PaintingBinding.instance.imageCache;

      // 판매 현장에 맞춘 캐시 크기 설정 (성능 최적화)
      imageCache.maximumSize = 500; // 더 많은 이미지 캐싱 (100→500)
      imageCache.maximumSizeBytes = 500 << 20; // 500MB로 설정 (50MB→500MB)

      LoggerUtils.logInfo(
        'Image cache initialized with optimized settings - Size: ${imageCache.maximumSize}, Bytes: ${(imageCache.maximumSizeBytes / (1024 * 1024)).toInt()}MB',
        tag: _tag,
      );
    } catch (e) {
      LoggerUtils.logError('Failed to initialize image cache', tag: _tag, error: e);
    }
  }

  /// 최적화된 네트워크 이미지 위젯 (판매 현장용)
  static Widget buildCachedNetworkImage({
    required String imageUrl,
    double? width,
    double? height,
    BoxFit fit = BoxFit.cover,
    PlaceholderWidgetBuilder? placeholder,
    LoadingErrorWidgetBuilder? errorWidget,
    String? cacheKey,
  }) {
    return CachedNetworkImage(
      imageUrl: imageUrl,
      cacheManager: _cacheManager,
      width: width,
      height: height,
      fit: fit,
      cacheKey: cacheKey,
      placeholder: placeholder ??
          (context, url) => Container(
                width: width,
                height: height,
                color: Colors.grey[300],
                child: const Center(
                  child: SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  ),
                ),
              ),
      errorWidget: errorWidget ??
          (context, url, error) => Container(
                width: width,
                height: height,
                color: Colors.grey[200],
                child: const Icon(Icons.image_not_supported, color: Colors.grey),
              ),
      memCacheWidth: width?.toInt(),
      memCacheHeight: height?.toInt(),
    );
  }

  /// 최적화된 파일 이미지 위젯 (로컬 파일용)
  static Widget buildOptimizedFileImage({
    required String imagePath,
    double? width,
    double? height,
    BoxFit fit = BoxFit.cover,
    Widget? placeholder,
    Widget? errorWidget,
  }) {
    return FutureBuilder<bool>(
      future: File(imagePath).exists(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return placeholder ??
              Container(
                width: width,
                height: height,
                color: Colors.grey[300],
                child: const Center(child: CircularProgressIndicator()),
              );
        }

        if (snapshot.hasData && snapshot.data == true) {
          return Image.file(
            File(imagePath),
            width: width,
            height: height,
            fit: fit,
            cacheWidth: width?.toInt(),
            cacheHeight: height?.toInt(),
          );
        }

        return errorWidget ??
            Container(
              width: width,
              height: height,
              color: Colors.grey[200],
              child: const Icon(Icons.image_not_supported, color: Colors.grey),
            );
      },
    );
  }

  /// 스마트 이미지 위젯 (URL 또는 로컬 파일 자동 판단)
  static Widget buildSmartImage({
    required String imagePath,
    double? width,
    double? height,
    BoxFit fit = BoxFit.cover,
    Widget? placeholder,
    Widget? errorWidget,
  }) {
    // URL인지 로컬 파일인지 판단
    if (imagePath.startsWith('http://') || imagePath.startsWith('https://')) {
      return buildCachedNetworkImage(
        imageUrl: imagePath,
        width: width,
        height: height,
        fit: fit,
        placeholder: placeholder != null ? (context, url) => placeholder : null,
        errorWidget: errorWidget != null ? (context, url, error) => errorWidget : null,
      );
    } else {
      return buildOptimizedFileImage(
        imagePath: imagePath,
        width: width,
        height: height,
        fit: fit,
        placeholder: placeholder,
        errorWidget: errorWidget,
      );
    }
  }

  /// 이미지 압축 (고품질 유지하면서 용량 최적화)
  static Future<Uint8List?> compressImage(
    Uint8List imageBytes, {
    int maxWidth = 1024,
    int maxHeight = 1024,
    int quality = 85,
  }) async {
    try {
      // 이미지 디코딩
      final image = img.decodeImage(imageBytes);
      if (image == null) return null;

      // 리사이징 (비율 유지)
      img.Image resized = image;
      if (image.width > maxWidth || image.height > maxHeight) {
        resized = img.copyResize(
          image,
          width: image.width > image.height ? maxWidth : null,
          height: image.height > image.width ? maxHeight : null,
        );
      }

      // JPEG로 인코딩 (품질 최적화)
      final compressedBytes = img.encodeJpg(resized, quality: quality);
      
      LoggerUtils.logInfo(
        'Image compressed: ${imageBytes.length} → ${compressedBytes.length} bytes',
        tag: _tag,
      );
      
      return Uint8List.fromList(compressedBytes);
    } catch (e) {
      LoggerUtils.logError('Failed to compress image', tag: _tag, error: e);
      return null;
    }
  }

  /// 전체 캐시 정리 (네트워크 이미지만)
  static Future<void> clearCache() async {
    try {
      await _cacheManager.emptyCache();
      LoggerUtils.logInfo('Network image cache cleared', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('Failed to clear network cache', tag: _tag, error: e);
    }
  }

  /// 특정 이벤트 캐시 정리
  static Future<void> clearEventCache(int eventId) async {
    try {
      // 이벤트 관련 캐시키 패턴으로 삭제
      await _cacheManager.removeFile('event_$eventId');
      LoggerUtils.logInfo('Event $eventId cache cleared', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('Failed to clear event cache', tag: _tag, error: e);
    }
  }

  /// 로컬 캐시 정리 (Flutter 이미지 캐시)
  static Future<void> clearAllLocalCache() async {
    try {
      await ImageSyncUtils.clearAllImageCache();
      LoggerUtils.logInfo('전체 로컬 이미지 캐시 정리 완료', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('전체 로컬 이미지 캐시 정리 실패', tag: _tag, error: e);
    }
  }

  /// 모든 캐시 정리 (네트워크 + 로컬)
  static Future<void> clearAllCache() async {
    try {
      await clearCache(); // 네트워크 캐시
      await clearAllLocalCache(); // 로컬 캐시
      LoggerUtils.logInfo('All image cache cleared', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('Failed to clear all cache', tag: _tag, error: e);
    }
  }

  /// 캐시 정보 가져오기
  static Map<String, dynamic> getCacheInfo() {
    final imageCache = PaintingBinding.instance.imageCache;
    return {
      'currentSize': imageCache.currentSize,
      'maximumSize': imageCache.maximumSize,
      'currentSizeBytes': imageCache.currentSizeBytes,
      'maximumSizeBytes': imageCache.maximumSizeBytes,
      'liveImageCount': imageCache.liveImageCount,
      'pendingImageCount': imageCache.pendingImageCount,
    };
  }

  /// 메모리 사용량 최적화 (더 적극적인 정리)
  static void optimizeMemoryUsage() {
    try {
      final imageCache = PaintingBinding.instance.imageCache;

      // 현재 캐시 사용량 확인
      final currentSizeBytes = imageCache.currentSizeBytes;
      final maxSizeBytes = imageCache.maximumSizeBytes;
      final currentSize = imageCache.currentSize;
      final maxSize = imageCache.maximumSize;

      // 85% 이상 사용 시에만 캐시 정리 (판매 중 성능 우선)
      if (currentSizeBytes > maxSizeBytes * 0.85 || currentSize > maxSize * 0.85) {
        // 오래된 이미지만 정리 (현재 보고 있는 이미지는 유지)
        imageCache.clearLiveImages();
        LoggerUtils.logInfo('Memory optimized - cleared old images only', tag: _tag);

        // 95% 이상일 때만 전체 정리
        if (currentSizeBytes > maxSizeBytes * 0.95) {
          imageCache.clear();
          LoggerUtils.logInfo('Memory critically optimized - cleared all images', tag: _tag);
        }
      }

      // 커스텀 캐시 매니저는 90% 이상일 때만 정리
      if (currentSizeBytes > maxSizeBytes * 0.9) {
        _cacheManager.emptyCache();
        LoggerUtils.logInfo('Custom cache manager cleared', tag: _tag);
      }
    } catch (e) {
      LoggerUtils.logError('Failed to optimize memory usage', tag: _tag, error: e);
    }
  }

  /// 정기적인 캐시 최적화 실행 (판매 중 성능 우선)
  static void schedulePeriodicOptimization() {
    // 10분마다 메모리 최적화 실행 (판매 중 방해 최소화)
    Stream.periodic(const Duration(minutes: 10)).listen((_) {
      optimizeMemoryUsage();
    });
  }
}

/// 이미지 상태 캐싱 클래스
/// 
/// 이미지 파일의 존재 여부와 로딩 상태를 메모리에 캐싱하여
/// 반복적인 파일 시스템 접근을 방지하고 UI 깜빡임을 줄입니다.
class ImageStateCache {
  static const String _tag = 'ImageStateCache';
  
  // 파일 존재 여부 캐시
  static final Map<String, bool> _existsCache = {};
  
  // 로딩 상태 캐시
  static final Map<String, bool> _loadingCache = {};
  
  // 캐시 만료 시간 (5분)
  static const Duration _cacheExpiry = Duration(minutes: 5);
  
  // 캐시 타임스탬프
  static final Map<String, DateTime> _cacheTimestamps = {};

  /// 파일 존재 여부를 캐시에서 확인
  static bool? getCachedExists(String imagePath) {
    if (_isCacheExpired(imagePath)) {
      _clearCacheForPath(imagePath);
      return null;
    }
    return _existsCache[imagePath];
  }

  /// 파일 존재 여부를 캐시에 저장
  static void setCachedExists(String imagePath, bool exists) {
    _existsCache[imagePath] = exists;
    _cacheTimestamps[imagePath] = DateTime.now();
    LoggerUtils.logDebug('이미지 존재 여부 캐시 저장: $imagePath = $exists', tag: _tag);
  }

  /// 로딩 상태를 캐시에서 확인
  static bool isLoading(String imagePath) {
    return _loadingCache[imagePath] ?? false;
  }

  /// 로딩 상태를 캐시에 저장
  static void setLoading(String imagePath, bool loading) {
    _loadingCache[imagePath] = loading;
    if (loading) {
      LoggerUtils.logDebug('이미지 로딩 시작: $imagePath', tag: _tag);
    } else {
      LoggerUtils.logDebug('이미지 로딩 완료: $imagePath', tag: _tag);
    }
  }

  /// 실제 파일 존재 여부 확인 (캐시 우선)
  static Future<bool> checkFileExists(String imagePath) async {
    // 1. 캐시에서 확인
    final cached = getCachedExists(imagePath);
    if (cached != null) {
      return cached;
    }

    // 2. 실제 파일 확인
    try {
      final file = File(imagePath);
      final exists = await file.exists();
      setCachedExists(imagePath, exists);
      return exists;
    } catch (e) {
      LoggerUtils.logError('파일 존재 확인 실패: $imagePath', tag: _tag, error: e);
      setCachedExists(imagePath, false);
      return false;
    }
  }

  /// 캐시가 만료되었는지 확인
  static bool _isCacheExpired(String imagePath) {
    final timestamp = _cacheTimestamps[imagePath];
    if (timestamp == null) return true;
    
    return DateTime.now().difference(timestamp) > _cacheExpiry;
  }

  /// 특정 경로의 캐시 삭제
  static void _clearCacheForPath(String imagePath) {
    _existsCache.remove(imagePath);
    _loadingCache.remove(imagePath);
    _cacheTimestamps.remove(imagePath);
    LoggerUtils.logDebug('이미지 캐시 삭제: $imagePath', tag: _tag);
  }

  /// 특정 경로의 캐시를 강제로 클리어 (공개 메서드)
  static void clearCacheForPath(String imagePath) {
    _clearCacheForPath(imagePath);
  }

  /// 모든 캐시 클리어
  static void clearAllCache() {
    final count = _existsCache.length;
    _existsCache.clear();
    _loadingCache.clear();
    _cacheTimestamps.clear();
    LoggerUtils.logInfo('모든 이미지 캐시 클리어 완료: ${count}개', tag: _tag);
  }

  /// 특정 디렉토리의 모든 캐시 클리어
  static void clearCacheForDirectory(String directoryPath) {
    final pathsToRemove = <String>[];
    for (final path in _existsCache.keys) {
      if (path.startsWith(directoryPath)) {
        pathsToRemove.add(path);
      }
    }

    for (final path in pathsToRemove) {
      _clearCacheForPath(path);
    }

    LoggerUtils.logInfo('디렉토리 캐시 클리어 완료: $directoryPath (${pathsToRemove.length}개)', tag: _tag);
  }



  /// 만료된 캐시 정리
  static void cleanupExpiredCache() {
    final now = DateTime.now();
    final expiredKeys = <String>[];
    
    for (final entry in _cacheTimestamps.entries) {
      if (now.difference(entry.value) > _cacheExpiry) {
        expiredKeys.add(entry.key);
      }
    }
    
    for (final key in expiredKeys) {
      _clearCacheForPath(key);
    }
    
    if (expiredKeys.isNotEmpty) {
      LoggerUtils.logInfo('만료된 이미지 캐시 ${expiredKeys.length}개 정리', tag: _tag);
    }
  }

  /// 캐시 상태 정보
  static Map<String, dynamic> getCacheInfo() {
    return {
      'existsCache': _existsCache.length,
      'loadingCache': _loadingCache.length,
      'timestamps': _cacheTimestamps.length,
    };
  }
}
