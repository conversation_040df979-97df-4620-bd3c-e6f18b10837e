import 'dart:io';
import 'package:firebase_storage/firebase_storage.dart';
import 'logger_utils.dart';
import 'firebase_service_wrapper.dart';

/// Firebase Storage 업로드 유틸리티 클래스
/// - 안정적인 파일 업로드 기능 제공
/// - 에러 핸들링 및 재시도 로직 포함
/// - 업로드 진행률 모니터링 지원
class FirebaseUploadUtils {
  
  /// 파일을 Firebase Storage에 업로드
  /// 
  /// [file] 업로드할 파일
  /// [path] Storage 내 저장 경로 (예: 'profile_images/user123.jpg')
  /// [onProgress] 업로드 진행률 콜백 (0.0 ~ 1.0)
  /// [maxRetries] 최대 재시도 횟수
  /// 
  /// Returns: 업로드된 파일의 다운로드 URL
  static Future<String> uploadFile(
    File file, 
    String path, {
    void Function(double progress)? onProgress,
    int maxRetries = 3,
  }) async {
    if (!await file.exists()) {
      throw Exception('업로드할 파일이 존재하지 않습니다: ${file.path}');
    }

    // App Check 준비 상태 확인
    LoggerUtils.logDebug('Firebase Storage 업로드 전 App Check 확인', tag: 'FirebaseUpload');
    await FirebaseServiceWrapper.ensureAppCheckReady();

    final storageRef = FirebaseStorage.instance.ref().child(path);
    
    for (int attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        LoggerUtils.logDebug(
          'Firebase Storage 업로드 시작 (시도 $attempt/$maxRetries): $path',
          tag: 'FirebaseUpload',
        );

        // 업로드 태스크 생성
        final uploadTask = storageRef.putFile(file);
        
        // 진행률 모니터링
        if (onProgress != null) {
          uploadTask.snapshotEvents.listen(
            (TaskSnapshot snapshot) {
              final progress = snapshot.bytesTransferred / snapshot.totalBytes;
              onProgress(progress);
              
              LoggerUtils.logDebug(
                'Upload progress: ${(progress * 100).toStringAsFixed(1)}%',
                tag: 'FirebaseUpload',
              );
            },
            onError: (error) {
              LoggerUtils.logError(
                'Upload progress error: $error',
                error: error,
                tag: 'FirebaseUpload',
              );
            },
          );
        }

        // 업로드 완료 대기
        final snapshot = await uploadTask;
        
        // 상태 확인
        if (snapshot.state == TaskState.success) {
          final downloadUrl = await storageRef.getDownloadURL();
          
          LoggerUtils.logInfo(
            'Firebase Storage 업로드 완료: $path',
            tag: 'FirebaseUpload',
          );
          
          return downloadUrl;
        } else {
          throw Exception('업로드 실패: ${snapshot.state}');
        }
        
      } catch (e) {
        LoggerUtils.logError(
          'Firebase Storage 업로드 실패 (시도 $attempt/$maxRetries): $e',
          error: e,
          tag: 'FirebaseUpload',
        );
        
        if (attempt == maxRetries) {
          // 최대 재시도 횟수에 도달
          rethrow;
        }
        
        // 재시도 전 잠시 대기
        await Future.delayed(Duration(seconds: attempt));
      }
    }
    
    throw Exception('업로드 실패: 최대 재시도 횟수 초과');
  }

  /// 이미지 파일을 압축하여 업로드
  /// 
  /// [file] 업로드할 이미지 파일
  /// [path] Storage 내 저장 경로
  /// [maxWidth] 최대 가로 크기 (기본: 1024)
  /// [maxHeight] 최대 세로 크기 (기본: 1024)
  /// [quality] JPEG 압축 품질 (0-100, 기본: 85)
  /// [onProgress] 업로드 진행률 콜백
  /// 
  /// Returns: 업로드된 파일의 다운로드 URL
  static Future<String> uploadImageWithCompression(
    File file,
    String path, {
    int maxWidth = 1024,
    int maxHeight = 1024,
    int quality = 85,
    void Function(double progress)? onProgress,
  }) async {
    try {
      // 이미지 압축 로직은 기존 코드 사용
      // (addWhitePaddingAndCenterImage 등)
      
      // 임시로 원본 파일 업로드
      return await uploadFile(file, path, onProgress: onProgress);
      
    } catch (e) {
      LoggerUtils.logError(
        'Image upload with compression failed: $e',
        error: e,
        tag: 'FirebaseUpload',
      );
      rethrow;
    }
  }

  /// 업로드 중인 태스크 취소
  /// 
  /// [uploadTask] 취소할 업로드 태스크
  static Future<bool> cancelUpload(UploadTask uploadTask) async {
    try {
      await uploadTask.cancel();
      LoggerUtils.logInfo('Upload task cancelled', tag: 'FirebaseUpload');
      return true;
    } catch (e) {
      LoggerUtils.logError(
        'Failed to cancel upload task: $e',
        error: e,
        tag: 'FirebaseUpload',
      );
      return false;
    }
  }

  /// Storage 파일 삭제
  /// 
  /// [path] 삭제할 파일 경로
  static Future<bool> deleteFile(String path) async {
    try {
      final storageRef = FirebaseStorage.instance.ref().child(path);
      await storageRef.delete();
      
      LoggerUtils.logInfo(
        'Firebase Storage file deleted: $path',
        tag: 'FirebaseUpload',
      );
      
      return true;
    } catch (e) {
      LoggerUtils.logError(
        'Failed to delete Firebase Storage file: $e',
        error: e,
        tag: 'FirebaseUpload',
      );
      return false;
    }
  }

  /// 파일 존재 여부 확인
  ///
  /// [path] 확인할 파일 경로
  static Future<bool> fileExists(String path) async {
    try {
      final storageRef = FirebaseStorage.instance.ref().child(path);
      await storageRef.getMetadata();
      return true;
    } catch (e) {
      return false;
    }
  }

  /// 행사 관련 Storage 폴더 전체 삭제
  ///
  /// [userId] 사용자 ID
  /// [eventId] 삭제할 행사 ID
  static Future<bool> deleteEventStorageFolder(String userId, int eventId) async {
    try {
      LoggerUtils.logInfo(
        'Firebase Storage 행사 폴더 삭제 시작: users/$userId/events/$eventId/',
        tag: 'FirebaseUpload',
      );

      final storage = FirebaseStorage.instance;

      // 해당 행사 폴더의 모든 파일 목록 조회
      final listResult = await storage.ref().child('users/$userId/events/$eventId').listAll();

      int deletedCount = 0;

      // 모든 파일 삭제
      for (final item in listResult.items) {
        try {
          await item.delete();
          deletedCount++;
          LoggerUtils.logDebug('파일 삭제 완료: ${item.fullPath}', tag: 'FirebaseUpload');
        } catch (e) {
          LoggerUtils.logWarning('파일 삭제 실패: ${item.fullPath}', tag: 'FirebaseUpload', error: e);
        }
      }

      // 하위 폴더들도 재귀적으로 삭제
      for (final prefix in listResult.prefixes) {
        try {
          await _deleteStorageFolderRecursive(prefix);
          LoggerUtils.logDebug('하위 폴더 삭제 완료: ${prefix.fullPath}', tag: 'FirebaseUpload');
        } catch (e) {
          LoggerUtils.logWarning('하위 폴더 삭제 실패: ${prefix.fullPath}', tag: 'FirebaseUpload', error: e);
        }
      }

      LoggerUtils.logInfo(
        'Firebase Storage 행사 폴더 삭제 완료: $deletedCount개 파일 삭제',
        tag: 'FirebaseUpload',
      );

      return true;
    } catch (e) {
      LoggerUtils.logError(
        'Firebase Storage 행사 폴더 삭제 실패: users/$userId/events/$eventId/',
        error: e,
        tag: 'FirebaseUpload',
      );
      return false;
    }
  }

  /// Storage 폴더 재귀적 삭제 (내부 헬퍼 메서드)
  static Future<void> _deleteStorageFolderRecursive(Reference folderRef) async {
    final listResult = await folderRef.listAll();

    // 모든 파일 삭제
    for (final item in listResult.items) {
      await item.delete();
    }

    // 하위 폴더들도 재귀적으로 삭제
    for (final prefix in listResult.prefixes) {
      await _deleteStorageFolderRecursive(prefix);
    }
  }
}
