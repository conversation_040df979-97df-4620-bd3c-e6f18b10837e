/// 바라 부스 매니저 - 행사 권한 Repository
///
/// 행사 권한 데이터의 CRUD 작업을 담당하는 Repository 클래스입니다.
/// - Firebase Firestore 연동
/// - 권한 생성, 조회, 수정, 삭제
/// - 권한 관리
///
/// 작성자: Blue
/// 버전: 1.0.0
/// 최종 업데이트: 2025년 8월
library;

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../models/event_permission.dart';
import '../models/user_role.dart';
import '../utils/logger_utils.dart';

/// 행사 권한 Repository 클래스
class EventPermissionRepository {
  static const String _tag = 'EventPermissionRepository';
  
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;

  /// 권한 생성
  ///
  /// [permission] 생성할 권한 정보
  /// 반환값: 생성된 권한 정보
  Future<EventPermission> createPermission(EventPermission permission) async {
    LoggerUtils.methodStart('createPermission', tag: _tag, data: {
      'eventId': permission.eventId,
      'userId': permission.userId,
      'role': permission.role.value,
    });

    try {
      // 현재 사용자 인증 확인
      final currentUser = _auth.currentUser;
      if (currentUser == null) {
        throw Exception('로그인이 필요합니다');
      }

      // 권한 부여자가 현재 사용자와 일치하는지 확인
      if (permission.grantedByUserId != currentUser.uid) {
        throw Exception('권한이 없습니다');
      }

      await _firestore
          .collection('event_permissions')
          .doc(permission.eventId.toString())
          .collection('users')
          .doc(permission.userId)
          .set(permission.toFirebaseMap());

      LoggerUtils.logInfo('권한 생성 완료: ${permission.userNickname}', tag: _tag);
      return permission;
    } catch (e) {
      LoggerUtils.logError('권한 생성 실패', tag: _tag, error: e);
      rethrow;
    }
  }

  /// 사용자 권한 조회
  /// 
  /// [eventId] 행사 ID
  /// [userId] 사용자 ID
  /// 반환값: 권한 정보 (없으면 null)
  Future<EventPermission?> getPermission(int eventId, String userId) async {
    LoggerUtils.methodStart('getPermission', tag: _tag, data: {
      'eventId': eventId,
      'userId': userId,
    });

    try {
      final doc = await _firestore
          .collection('event_permissions')
          .doc(eventId.toString())
          .collection('users')
          .doc(userId)
          .get();

      if (!doc.exists) {
        LoggerUtils.logInfo('권한을 찾을 수 없음: eventId=$eventId, userId=$userId', tag: _tag);
        return null;
      }

      final permission = EventPermission.fromFirebaseMap(doc.data()!);
      LoggerUtils.logInfo('권한 조회 완료: ${permission.userNickname}', tag: _tag);
      return permission;
    } catch (e) {
      LoggerUtils.logError('권한 조회 실패', tag: _tag, error: e);
      rethrow;
    }
  }

  /// 행사의 모든 권한 조회
  /// 
  /// [eventId] 행사 ID
  /// 반환값: 권한 목록
  Future<List<EventPermission>> getEventPermissions(int eventId) async {
    LoggerUtils.methodStart('getEventPermissions', tag: _tag, data: {
      'eventId': eventId,
    });

    try {
      final snapshot = await _firestore
          .collection('event_permissions')
          .doc(eventId.toString())
          .collection('users')
          .orderBy('grantedAt', descending: true)
          .get();

      final permissions = <EventPermission>[];
      for (final doc in snapshot.docs) {
        try {
          final permission = EventPermission.fromFirebaseMap(doc.data());
          permissions.add(permission);
        } catch (e) {
          LoggerUtils.logWarning('권한 데이터 파싱 실패: ${doc.id}', tag: _tag, error: e);
        }
      }

      LoggerUtils.logInfo('행사 권한 목록 조회 완료: ${permissions.length}개', tag: _tag);
      return permissions;
    } catch (e) {
      LoggerUtils.logError('행사 권한 목록 조회 실패', tag: _tag, error: e);
      rethrow;
    }
  }

  /// 사용자가 접근 가능한 행사 목록 조회
  /// 
  /// [userId] 사용자 ID
  /// 반환값: 접근 가능한 행사 ID 목록
  Future<List<int>> getAccessibleEventIds(String userId) async {
    LoggerUtils.methodStart('getAccessibleEventIds', tag: _tag, data: {
      'userId': userId,
    });

    try {
      // collectionGroup을 사용하여 모든 event_permissions에서 해당 사용자 검색
      final snapshot = await _firestore
          .collectionGroup('users')
          .where('userId', isEqualTo: userId)
          .get();

      final eventIds = <int>[];
      for (final doc in snapshot.docs) {
        try {
          final permission = EventPermission.fromFirebaseMap(doc.data());
          eventIds.add(permission.eventId);
        } catch (e) {
          LoggerUtils.logWarning('권한 데이터 파싱 실패: ${doc.id}', tag: _tag, error: e);
        }
      }

      LoggerUtils.logInfo('접근 가능한 행사 ${eventIds.length}개 조회 완료', tag: _tag);
      return eventIds;
    } catch (e) {
      LoggerUtils.logError('접근 가능한 행사 목록 조회 실패', tag: _tag, error: e);
      rethrow;
    }
  }

  /// 권한 업데이트
  /// 
  /// [permission] 업데이트할 권한 정보
  /// 반환값: 업데이트된 권한 정보
  Future<EventPermission> updatePermission(EventPermission permission) async {
    LoggerUtils.methodStart('updatePermission', tag: _tag, data: {
      'eventId': permission.eventId,
      'userId': permission.userId,
    });

    try {
      final updatedPermission = permission.copyWith(
        updatedAt: DateTime.now(),
      );

      await _firestore
          .collection('event_permissions')
          .doc(permission.eventId.toString())
          .collection('users')
          .doc(permission.userId)
          .update(updatedPermission.toFirebaseMap());

      LoggerUtils.logInfo('권한 업데이트 완료: ${permission.userNickname}', tag: _tag);
      return updatedPermission;
    } catch (e) {
      LoggerUtils.logError('권한 업데이트 실패', tag: _tag, error: e);
      rethrow;
    }
  }

  /// 권한 삭제
  /// 
  /// [eventId] 행사 ID
  /// [userId] 사용자 ID
  Future<void> deletePermission(int eventId, String userId) async {
    LoggerUtils.methodStart('deletePermission', tag: _tag, data: {
      'eventId': eventId,
      'userId': userId,
    });

    try {
      await _firestore
          .collection('event_permissions')
          .doc(eventId.toString())
          .collection('users')
          .doc(userId)
          .delete();

      LoggerUtils.logInfo('권한 삭제 완료: eventId=$eventId, userId=$userId', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('권한 삭제 실패', tag: _tag, error: e);
      rethrow;
    }
  }

  /// 특정 역할의 사용자 목록 조회
  /// 
  /// [eventId] 행사 ID
  /// [role] 사용자 역할
  /// 반환값: 해당 역할의 권한 목록
  Future<List<EventPermission>> getPermissionsByRole(int eventId, UserRole role) async {
    LoggerUtils.methodStart('getPermissionsByRole', tag: _tag, data: {
      'eventId': eventId,
      'role': role.value,
    });

    try {
      final snapshot = await _firestore
          .collection('event_permissions')
          .doc(eventId.toString())
          .collection('users')
          .where('role', isEqualTo: role.value)
          .orderBy('grantedAt', descending: true)
          .get();

      final permissions = <EventPermission>[];
      for (final doc in snapshot.docs) {
        try {
          final permission = EventPermission.fromFirebaseMap(doc.data());
          permissions.add(permission);
        } catch (e) {
          LoggerUtils.logWarning('권한 데이터 파싱 실패: ${doc.id}', tag: _tag, error: e);
        }
      }

      LoggerUtils.logInfo('역할별 권한 목록 조회 완료: ${permissions.length}개', tag: _tag);
      return permissions;
    } catch (e) {
      LoggerUtils.logError('역할별 권한 목록 조회 실패', tag: _tag, error: e);
      rethrow;
    }
  }

  /// 초대받은 사용자 목록 조회
  /// 
  /// [eventId] 행사 ID
  /// 반환값: 초대받은 사용자 권한 목록
  Future<List<EventPermission>> getInvitedUsers(int eventId) async {
    return await getPermissionsByRole(eventId, UserRole.invited);
  }

  /// 사용자 닉네임 업데이트
  /// 
  /// [userId] 사용자 ID
  /// [newNickname] 새 닉네임
  /// 반환값: 업데이트된 권한 수
  Future<int> updateUserNickname(String userId, String newNickname) async {
    LoggerUtils.methodStart('updateUserNickname', tag: _tag, data: {
      'userId': userId,
      'newNickname': newNickname,
    });

    try {
      // 해당 사용자의 모든 권한 조회
      final snapshot = await _firestore
          .collectionGroup('users')
          .where('userId', isEqualTo: userId)
          .get();

      if (snapshot.docs.isEmpty) {
        LoggerUtils.logInfo('업데이트할 권한이 없음', tag: _tag);
        return 0;
      }

      final batch = _firestore.batch();
      for (final doc in snapshot.docs) {
        batch.update(doc.reference, {
          'userNickname': newNickname,
          'updatedAt': DateTime.now().toIso8601String(),
        });
      }

      await batch.commit();
      LoggerUtils.logInfo('사용자 닉네임 업데이트 완료: ${snapshot.docs.length}개', tag: _tag);
      return snapshot.docs.length;
    } catch (e) {
      LoggerUtils.logError('사용자 닉네임 업데이트 실패', tag: _tag, error: e);
      rethrow;
    }
  }

  /// 행사의 모든 권한 삭제 (행사 삭제 시 사용)
  /// 
  /// [eventId] 행사 ID
  /// 반환값: 삭제된 권한 수
  Future<int> deleteAllEventPermissions(int eventId) async {
    LoggerUtils.methodStart('deleteAllEventPermissions', tag: _tag, data: {
      'eventId': eventId,
    });

    try {
      final snapshot = await _firestore
          .collection('event_permissions')
          .doc(eventId.toString())
          .collection('users')
          .get();

      if (snapshot.docs.isEmpty) {
        LoggerUtils.logInfo('삭제할 권한이 없음', tag: _tag);
        return 0;
      }

      final batch = _firestore.batch();
      for (final doc in snapshot.docs) {
        batch.delete(doc.reference);
      }

      await batch.commit();
      LoggerUtils.logInfo('행사 권한 전체 삭제 완료: ${snapshot.docs.length}개', tag: _tag);
      return snapshot.docs.length;
    } catch (e) {
      LoggerUtils.logError('행사 권한 전체 삭제 실패', tag: _tag, error: e);
      rethrow;
    }
  }
}
