/// 바라 부스 매니저 - 구독 관리 화면
///
/// 실제 운영 환경의 깔끔한 구독 관리 화면입니다.
/// - 나이스페이 v1 API 사용
/// - 실제 운영 키 사용
/// - Firebase Auth 연동
/// - 깔끔한 UI/UX
///
/// 작성자: Blue
/// 버전: 2.0.0
/// 최종 업데이트: 2025년 8월
library;

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../../services/nicepay_subscription_service.dart';
import '../../utils/logger_utils.dart';
import '../../utils/toast_utils.dart';
import '../../widgets/app_bar_styles.dart';
import '../../utils/app_colors.dart';
import '../../utils/proration_utils.dart';
import '../../models/subscription_plan.dart';
import '../../providers/subscription_provider.dart';
import 'subscription_card_registration_screen.dart';

/// 구독 관리 화면
class SubscriptionScreen extends ConsumerStatefulWidget {
  const SubscriptionScreen({super.key});

  @override
  ConsumerState<SubscriptionScreen> createState() => _SubscriptionScreenState();
}

class _SubscriptionScreenState extends ConsumerState<SubscriptionScreen> {
  static const String _tag = 'SubscriptionScreen';
  
  final NicePaySubscriptionService _subscriptionService = NicePaySubscriptionService();
  
  bool _isLoading = false;
  Map<String, dynamic>? _registeredCard;
  List<Map<String, dynamic>> _paymentHistory = [];
  bool _isCancelScheduled = false;
  


  @override
  void initState() {
    super.initState();
    _loadData();
  }

  /// 데이터 로드
  Future<void> _loadData() async {
    setState(() => _isLoading = true);
    
    try {
      final results = await Future.wait([
        _subscriptionService.getRegisteredCard(),
        _subscriptionService.getPaymentHistory(),
        _subscriptionService.getCurrentSubscription(),
      ]);

      setState(() {
        _registeredCard = results[0] as Map<String, dynamic>?;
        _paymentHistory = results[1] as List<Map<String, dynamic>>;
        final rawSubscriptionData = results[2] as Map<String, dynamic>?;
        _isCancelScheduled = rawSubscriptionData?['status'] == 'cancel_scheduled';
      });

      // 디버깅용 로그
      if (_registeredCard != null) {
        LoggerUtils.logInfo('등록된 카드 정보: $_registeredCard', tag: _tag);
        LoggerUtils.logInfo('maskedCardNo: ${_registeredCard!['maskedCardNo']}', tag: _tag);
        print('DEBUG - 카드 정보: $_registeredCard');
        print('DEBUG - maskedCardNo: ${_registeredCard!['maskedCardNo']}');
      }
    } catch (e) {
      LoggerUtils.logError('데이터 로드 오류: $e', tag: _tag);
      ToastUtils.showError(context, '데이터를 불러오는 중 오류가 발생했습니다.');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Builder(builder: (ctx) => Text('구독 관리', style: AppBarStyles.of(ctx))),
        centerTitle: true,
        backgroundColor: AppColors.primarySeed,
        foregroundColor: Colors.white,
        surfaceTintColor: Colors.transparent,
        elevation: 0,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : RefreshIndicator(
              onRefresh: _loadData,
              child: SingleChildScrollView(
                physics: const AlwaysScrollableScrollPhysics(),
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildSubscriptionStatus(),
                    const SizedBox(height: 24),
                    _buildCardSection(),
                    const SizedBox(height: 24),
                    _buildPaymentHistory(),
                  ],
                ),
              ),
            ),
    );
  }

  /// 구독 상태 섹션
  Widget _buildSubscriptionStatus() {
    // subscription_provider의 상태를 직접 사용하여 실시간 업데이트 보장
    final currentPlanAsync = ref.watch(subscriptionNotifierProvider);
    final currentSubscriptionAsync = ref.watch(currentSubscriptionProvider);

    return currentPlanAsync.when(
      data: (currentPlanType) {
        return currentSubscriptionAsync.when(
          data: (subscription) {
            // 무료 플랜일 때는 isActive를 false로 설정하여 업그레이드 버튼들이 보이도록 함
            final isActive = subscription?.isValid ?? false;
            final isFreeOrInactive = currentPlanType == SubscriptionPlanType.free || !isActive;
            final planType = currentPlanType;

            // 플랜별 정보 설정
            String planName;
            String planDescription;
            MaterialColor planColor;

            if (isActive) {
              if (planType == SubscriptionPlanType.plus) {
                planName = '⭐ 플러스 플랜 활성화';
                planDescription = '월 3,500원 • 무제한 행사 • 무제한 상품 • 로컬 전용';
                planColor = Colors.blue;
              } else if (planType == SubscriptionPlanType.pro) {
                planName = '⭐ 프로 플랜 활성화';
                planDescription = '월 4,900원 • 무제한 행사 • 무제한 상품 • 클라우드 동기화';
                planColor = Colors.amber;
              } else {
                planName = '☆ 무료 플랜';
                planDescription = '행사 1개 제한 • 상품 30개 제한';
                planColor = Colors.grey;
              }
            } else {
              planName = '☆ 무료 플랜';
              planDescription = '행사 1개 제한 • 상품 30개 제한';
              planColor = Colors.grey;
            }

            return _buildSubscriptionStatusCard(
              planName: planName,
              planDescription: planDescription,
              planColor: planColor,
              isActive: !isFreeOrInactive, // 무료 플랜이거나 비활성화 상태면 false
              subscription: subscription,
              planType: planType,
              isCancelScheduled: _isCancelScheduled,
            );
          },
          loading: () => const Card(
            elevation: 2,
            child: Padding(
              padding: EdgeInsets.all(16),
              child: Center(child: CircularProgressIndicator()),
            ),
          ),
          error: (error, stack) => Card(
            elevation: 2,
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  const Icon(Icons.error, color: Colors.red),
                  const SizedBox(height: 8),
                  Text('구독 상태를 불러올 수 없습니다'),
                  const SizedBox(height: 8),
                  ElevatedButton(
                    onPressed: () => ref.refresh(subscriptionNotifierProvider),
                    child: const Text('다시 시도'),
                  ),
                ],
              ),
            ),
          ),
        );
      },
      loading: () => const Card(
        elevation: 2,
        child: Padding(
          padding: EdgeInsets.all(16),
          child: Center(child: CircularProgressIndicator()),
        ),
      ),
      error: (error, stack) => Card(
        elevation: 2,
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              const Icon(Icons.error, color: Colors.red),
              const SizedBox(height: 8),
              Text('구독 상태를 불러올 수 없습니다'),
              const SizedBox(height: 8),
              ElevatedButton(
                onPressed: () => ref.refresh(subscriptionNotifierProvider),
                child: const Text('다시 시도'),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 구독 상태 카드 위젯
  Widget _buildSubscriptionStatusCard({
    required String planName,
    required String planDescription,
    required MaterialColor planColor,
    required bool isActive,
    required UserSubscription? subscription,
    required SubscriptionPlanType planType,
    required bool isCancelScheduled,
  }) {
    
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  isActive ? Icons.check_circle : Icons.cancel,
                  color: isActive ? planColor : Colors.grey,
                ),
                const SizedBox(width: 8),
                Text(
                  '구독 상태',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: isActive ? planColor.shade50 : Colors.grey.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: isActive ? planColor.shade200 : Colors.grey.shade300,
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    planName,
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: isActive ? planColor.shade700 : Colors.grey.shade700,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    planDescription,
                    style: TextStyle(
                      fontSize: 14,
                      color: isActive ? planColor.shade600 : Colors.grey.shade600,
                    ),
                  ),
                  if (isActive && subscription?.subscriptionEndDate != null) ...[
                    const SizedBox(height: 4),
                    Text(
                      isCancelScheduled
                        ? '구독 만료일: ${_formatDate(subscription!.subscriptionEndDate!)}'
                        : '다음 결제일: ${_formatDate(subscription!.subscriptionEndDate!)}',
                      style: TextStyle(
                        fontSize: 12,
                        color: isCancelScheduled ? Colors.orange.shade600 : planColor.shade600,
                      ),
                    ),
                  ],
                ],
              ),
            ),
            const SizedBox(height: 16),
            // 플랜별 버튼 표시 로직
            if (planType == SubscriptionPlanType.free) ...[
              // 무료 플랜: 플러스, 프로 플랜 버튼 표시
              Column(
                children: [
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton.icon(
                      onPressed: _registeredCard != null ? () => _startSubscription(SubscriptionPlanType.plus) : null,
                      icon: const Icon(Icons.star_half),
                      label: const Text('플러스 플랜 시작 (월 3,500원)'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.blue,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 12),
                      ),
                    ),
                  ),
                  const SizedBox(height: 8),
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton.icon(
                      onPressed: _registeredCard != null ? () => _startSubscription(SubscriptionPlanType.pro) : null,
                      icon: const Icon(Icons.star),
                      label: const Text('프로 플랜 시작 (월 4,900원)'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.amber,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 12),
                      ),
                    ),
                  ),
                ],
              ),
              if (_registeredCard == null) ...[
                const SizedBox(height: 8),
                Text(
                  '💡 구독을 시작하려면 먼저 카드를 등록해주세요.',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey.shade600,
                  ),
                ),
              ],
            ] else if (planType == SubscriptionPlanType.plus) ...[
              // 플러스 플랜: 프로 플랜 업그레이드 + 구독 취소 버튼 표시
              Column(
                children: [
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton.icon(
                      onPressed: _registeredCard != null ? () => _startSubscription(SubscriptionPlanType.pro) : null,
                      icon: const Icon(Icons.star),
                      label: const Text('프로 플랜으로 업그레이드 (월 4,900원)'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.amber,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 12),
                      ),
                    ),
                  ),
                  const SizedBox(height: 8),
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton.icon(
                      onPressed: isCancelScheduled ? _reactivateSubscription : _cancelSubscription,
                      icon: Icon(isCancelScheduled ? Icons.refresh : Icons.cancel),
                      label: Text(isCancelScheduled ? '재구독' : '구독 취소'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: isCancelScheduled ? Colors.green : Colors.red,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 12),
                      ),
                    ),
                  ),
                ],
              ),
            ] else if (planType == SubscriptionPlanType.pro) ...[
              // 프로 플랜: 구독 취소/재구독 버튼 표시
              SizedBox(
                width: double.infinity,
                child: ElevatedButton.icon(
                  onPressed: isCancelScheduled ? _reactivateSubscription : _cancelSubscription,
                  icon: Icon(isCancelScheduled ? Icons.refresh : Icons.cancel),
                  label: Text(isCancelScheduled ? '재구독' : '구독 취소'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: isCancelScheduled ? Colors.green : Colors.red,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 12),
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// 카드 섹션
  Widget _buildCardSection() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.credit_card, color: Colors.blue),
                const SizedBox(width: 8),
                Text(
                  '결제 카드',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            if (_registeredCard != null) ...[
              _buildRegisteredCard(),
            ] else ...[
              _buildCardRegistrationForm(),
            ],
          ],
        ),
      ),
    );
  }

  /// 등록된 카드 표시
  Widget _buildRegisteredCard() {
    final cardInfo = _registeredCard!['cardInfo'];

    return Column(
      children: [
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.blue.shade50,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.blue.shade200),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                '✅ 카드 등록 완료',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.blue.shade700,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                '카드명: ${cardInfo['cardName'] ?? '등록된 카드'}',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.blue.shade600,
                ),
              ),
              Text(
                '카드번호: ${_registeredCard!['maskedCardNo'] ?? '**** **** **** ****'}',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.blue.shade600,
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 12),
        SizedBox(
          width: double.infinity,
          child: ElevatedButton.icon(
            onPressed: _showCardRegistrationDialog,
            icon: const Icon(Icons.edit),
            label: const Text('카드 변경'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.orange,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 12),
            ),
          ),
        ),
      ],
    );
  }

  /// 카드 등록 폼
  Widget _buildCardRegistrationForm() {
    return Column(
      children: [
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.grey.shade50,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.grey.shade300),
          ),
          child: Text(
            '💳 카드를 등록하여 구독을 시작하세요',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey.shade700,
            ),
          ),
        ),
        const SizedBox(height: 12),
        SizedBox(
          width: double.infinity,
          child: ElevatedButton.icon(
            onPressed: _showCardRegistrationDialog,
            icon: const Icon(Icons.add_card),
            label: const Text('카드 등록'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 12),
            ),
          ),
        ),
      ],
    );
  }

  /// 결제 내역 섹션
  Widget _buildPaymentHistory() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.history, color: Colors.green),
                const SizedBox(width: 8),
                Text(
                  '결제 내역',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            if (_paymentHistory.isEmpty) ...[
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.grey.shade50,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.grey.shade300),
                ),
                child: Text(
                  '📋 결제 내역이 없습니다',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey.shade700,
                  ),
                ),
              ),
            ] else ...[
              ..._paymentHistory.take(5).map((payment) => _buildPaymentItem(payment)),
              if (_paymentHistory.length > 5) ...[
                const SizedBox(height: 8),
                Center(
                  child: TextButton(
                    onPressed: _showAllPaymentHistory,
                    child: Text('전체 내역 보기 (${_paymentHistory.length}건)'),
                  ),
                ),
              ],
            ],
          ],
        ),
      ),
    );
  }

  /// 결제 내역 아이템
  Widget _buildPaymentItem(Map<String, dynamic> payment) {
    final amount = payment['amount'] ?? 0;
    final status = payment['status'] ?? '';
    final description = payment['description'] ?? '';
    final createdAt = payment['createdAt'];
    final paymentData = payment['paymentData'] as Map<String, dynamic>?;

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.green.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.green.shade200),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Icon(
                status == 'paid' ? Icons.check_circle : Icons.error,
                color: status == 'paid' ? Colors.green : Colors.red,
                size: 20,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      description,
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(
                      '${amount}원 • ${_formatDate(createdAt)}',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey.shade600,
                      ),
                    ),
                  ],
                ),
              ),
              Text(
                status == 'paid' ? '완료' : '실패',
                style: TextStyle(
                  fontSize: 12,
                  color: status == 'paid' ? Colors.green : Colors.red,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          // 🔥 영수증 및 현금영수증 버튼 (결제 성공 시에만)
          if (status == 'paid' && paymentData != null) ...[
            const SizedBox(height: 8),
            Row(
              children: [
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: () => _showReceipt(paymentData),
                    icon: const Icon(Icons.receipt, size: 16),
                    label: const Text('영수증', style: TextStyle(fontSize: 12)),
                    style: OutlinedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 4),
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: () => _requestCashReceipt(paymentData),
                    icon: const Icon(Icons.receipt_long, size: 16),
                    label: const Text('현금영수증', style: TextStyle(fontSize: 12)),
                    style: OutlinedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 4),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  /// 날짜 포맷팅
  String _formatDate(dynamic timestamp) {
    if (timestamp == null) return '';

    DateTime date;
    if (timestamp is Timestamp) {
      date = timestamp.toDate();
    } else if (timestamp is DateTime) {
      date = timestamp;
    } else {
      return '';
    }

    return '${date.year}.${date.month.toString().padLeft(2, '0')}.${date.day.toString().padLeft(2, '0')}';
  }

  /// 문자열을 SubscriptionPlanType으로 변환
  SubscriptionPlanType? _parsePlanType(String? planString) {
    if (planString == null) return null;

    // "SubscriptionPlanType.plus" 형태에서 마지막 부분만 추출
    final planName = planString.contains('.')
        ? planString.split('.').last.toLowerCase()
        : planString.toLowerCase();

    switch (planName) {
      case 'plus':
        return SubscriptionPlanType.plus;
      case 'pro':
        return SubscriptionPlanType.pro;
      case 'free':
        return SubscriptionPlanType.free;
      default:
        return null;
    }
  }

  /// 구독 시작 또는 업그레이드
  Future<void> _startSubscription(SubscriptionPlanType planType) async {
    if (_registeredCard == null) {
      ToastUtils.showError(context, '먼저 카드를 등록해주세요.');
      return;
    }

    // 현재 구독 정보 확인
    final currentSubscription = await _subscriptionService.getCurrentSubscription();
    final currentPlanString = currentSubscription?['planType'] as String?;
    final currentPlanType = _parsePlanType(currentPlanString) ?? SubscriptionPlanType.free;

    // 프로레이션 적용 여부 확인
    bool needsProration = false;
    ProrationResult? prorationResult;

    if (currentPlanType == SubscriptionPlanType.plus && planType == SubscriptionPlanType.pro) {
      // 플러스 → 프로 업그레이드: 프로레이션 적용
      needsProration = true;
      final currentPlan = SubscriptionPlan(
        type: SubscriptionPlanType.plus,
        name: '플러스 플랜',
        description: '플러스 플랜',
        monthlyPrice: 3500,
        yearlyPrice: 42000,
      );
      final newPlan = SubscriptionPlan(
        type: SubscriptionPlanType.pro,
        name: '프로 플랜',
        description: '프로 플랜',
        monthlyPrice: 4900,
        yearlyPrice: 58800,
      );

      final endDateString = currentSubscription?['nextPaymentDate'] as String?;
      final endDate = endDateString != null
          ? DateTime.parse(endDateString)
          : DateTime.now().add(const Duration(days: 30));

      prorationResult = ProrationUtils.calculateProration(
        currentPlan: currentPlan,
        newPlan: newPlan,
        currentSubscriptionEndDate: endDate,
      );
    }

    // 플랜별 정보 설정
    String planName;
    String planFeatures;
    String actionText;

    if (planType == SubscriptionPlanType.plus) {
      planName = '플러스 플랜';
      planFeatures = '• 월 3,500원\n• 무제한 행사\n• 무제한 상품\n• 로컬 전용\n• 매월 자동 결제';
      actionText = currentPlanType == SubscriptionPlanType.free ? '구독 시작' : '플랜 변경';
    } else {
      planName = '프로 플랜';
      if (needsProration && prorationResult != null) {
        planFeatures = '• 월 4,900원\n• 무제한 행사\n• 무제한 상품\n• 클라우드 동기화\n• 매월 자동 결제\n\n💰 업그레이드 차액: ${prorationResult.chargeAmount}원\n(남은 ${prorationResult.remainingDays}일 기준)';
        actionText = '업그레이드';
      } else {
        planFeatures = '• 월 4,900원\n• 무제한 행사\n• 무제한 상품\n• 클라우드 동기화\n• 매월 자동 결제';
        actionText = currentPlanType == SubscriptionPlanType.free ? '구독 시작' : '플랜 변경';
      }
    }

    final confirmed = needsProration && prorationResult != null
        ? await _showProrationConfirmDialog(planName, prorationResult)
        : await _showConfirmDialog(
            actionText,
            '$planName으로 ${actionText.toLowerCase()}하시겠습니까?\n\n$planFeatures',
          );

    if (!confirmed) return;

    setState(() => _isLoading = true);

    try {
      SubscriptionResult result;

      if (needsProration && prorationResult != null) {
        // 프로레이션 결제 처리
        result = await _subscriptionService.processProrationPayment(
          _registeredCard!['bid'],
          prorationResult.chargeAmount,
          '프로 플랜 업그레이드 차액',
        );
      } else {
        // 일반 구독 시작
        result = await _subscriptionService.startSubscription(_registeredCard!['bid'], planType);
      }

      if (result.isSuccess) {
        try {
          // 구독 상태 업데이트
          await ref.read(subscriptionNotifierProvider.notifier).updatePlan(planType);
          LoggerUtils.logInfo('구독 상태 업데이트 완료: $planType', tag: _tag);

          // 관련 provider들 강제 갱신 (실시간 동기화 버튼 갱신을 위해)
          ref.invalidate(currentPlanTypeProvider);
          ref.invalidate(isProUserProvider);
          ref.invalidate(isPlusUserProvider);
          ref.invalidate(currentSubscriptionProvider);

          final message = needsProration
              ? '$planName 업그레이드가 완료되었습니다!'
              : '$planName 구독이 시작되었습니다!';
          ToastUtils.showSuccess(context, message);

          // 프로 플랜 업그레이드 시 실시간 동기화 안내
          if (planType == SubscriptionPlanType.pro) {
            _showRealtimeSyncGuide();
          }

          await _loadData();
        } catch (e) {
          LoggerUtils.logError('구독 상태 업데이트 실패: $e', tag: _tag);
          ToastUtils.showError(context, '구독은 성공했지만 상태 업데이트에 실패했습니다. 앱을 재시작해주세요.');
        }
      } else {
        final errorMessage = needsProration
            ? '업그레이드에 실패했습니다.'
            : '구독 시작에 실패했습니다.';
        ToastUtils.showError(context, result.error ?? errorMessage);
      }
    } catch (e) {
      LoggerUtils.logError('구독 처리 오류: $e', tag: _tag);
      ToastUtils.showError(context, '구독 처리 중 오류가 발생했습니다: ${e.toString()}');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  /// 재구독 (구독 취소 해제)
  Future<void> _reactivateSubscription() async {
    final confirmed = await _showConfirmDialog(
      '재구독',
      '구독을 다시 활성화하시겠습니까?\n다음 결제일에 정상적으로 결제가 진행됩니다.',
    );

    if (!confirmed) return;

    setState(() => _isLoading = true);

    try {
      // 구독 취소 상태 해제
      final rawSubscriptionData = await _subscriptionService.getCurrentSubscription();
      if (rawSubscriptionData != null) {
        await FirebaseFirestore.instance
            .collection('users')
            .doc(FirebaseAuth.instance.currentUser?.uid)
            .collection('subscriptions')
            .doc('current')
            .update({
          'status': 'active',
          'cancelledAt': FieldValue.delete(),
          'willCancelAt': FieldValue.delete(),
          'updatedAt': FieldValue.serverTimestamp(),
        });

        ToastUtils.showSuccess(context, '재구독이 완료되었습니다!');
        await _loadData();
      }
    } catch (e) {
      LoggerUtils.logError('재구독 실패: $e', tag: _tag);
      ToastUtils.showError(context, '재구독 처리 중 오류가 발생했습니다.');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  /// 구독 취소
  Future<void> _cancelSubscription() async {
    final confirmed = await _showCancelSubscriptionDialog();

    if (!confirmed) return;

    setState(() => _isLoading = true);

    try {
      final result = await _subscriptionService.cancelSubscription();

      if (result.isSuccess) {
        // 구독 취소는 즉시 무료 플랜으로 전환하지 않음 (다음 결제일까지 현재 플랜 유지)
        // 상태 새로고침만 수행
        ToastUtils.showSuccess(context, '구독 취소가 예약되었습니다. 다음 결제일까지 현재 플랜을 계속 사용할 수 있습니다.');
        await _loadData();
      } else {
        ToastUtils.showError(context, result.error ?? '구독 취소에 실패했습니다.');
      }
    } catch (e) {
      LoggerUtils.logError('구독 취소 오류: $e', tag: _tag);
      ToastUtils.showError(context, '구독 취소 중 오류가 발생했습니다.');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  /// 카드 등록 페이지로 이동
  Future<void> _showCardRegistrationDialog() async {
    final result = await Navigator.of(context).push<bool>(
      MaterialPageRoute(
        builder: (context) => SubscriptionCardRegistrationScreen(
          onSuccess: (bid) {
            LoggerUtils.logInfo('카드 등록 성공: BID=$bid', tag: _tag);
          },
        ),
      ),
    );

    // 카드 등록 성공 시 데이터 새로고침
    if (result == true) {
      await _loadData();
    }
  }



  /// 전체 결제 내역 보기
  void _showAllPaymentHistory() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('전체 결제 내역'),
        content: SizedBox(
          width: double.maxFinite,
          height: 400,
          child: ListView.builder(
            itemCount: _paymentHistory.length,
            itemBuilder: (context, index) => _buildPaymentItem(_paymentHistory[index]),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('닫기'),
          ),
        ],
      ),
    );
  }

  /// 확인 다이얼로그
  Future<bool> _showConfirmDialog(String title, String content) async {
    final result = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Text(content),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('취소'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('확인'),
          ),
        ],
      ),
    );
    return result ?? false;
  }

  /// 영수증 보기
  void _showReceipt(Map<String, dynamic> paymentData) {
    final receiptUrl = paymentData['receiptUrl'] as String?;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('영수증'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('거래번호: ${paymentData['tid'] ?? ''}'),
            Text('주문번호: ${paymentData['orderId'] ?? ''}'),
            Text('결제금액: ${paymentData['amount'] ?? 0}원'),
            Text('결제일시: ${paymentData['paidAt'] ?? ''}'),
            Text('카드정보: ${paymentData['card']?['cardName'] ?? ''} (${paymentData['card']?['cardNum'] ?? ''})'),
            if (receiptUrl != null) ...[
              const SizedBox(height: 12),
              Row(
                children: [
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: () => _openReceiptUrl(receiptUrl),
                      icon: const Icon(Icons.open_in_browser),
                      label: const Text('영수증 원본 보기'),
                    ),
                  ),
                  const SizedBox(width: 8),
                  IconButton(
                    onPressed: () => _copyReceiptUrl(receiptUrl),
                    icon: const Icon(Icons.copy),
                    tooltip: '링크 복사',
                    style: IconButton.styleFrom(
                      backgroundColor: Colors.grey[100],
                      foregroundColor: Colors.grey[700],
                    ),
                  ),
                ],
              ),
            ],
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('닫기'),
          ),
        ],
      ),
    );
  }

  /// 현금영수증 신청
  void _requestCashReceipt(Map<String, dynamic> paymentData) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('현금영수증 신청'),
        content: const Text(
          '현금영수증 신청은 나이스페이 고객센터(1588-3355)로 문의해주세요.\n\n'
          '또는 나이스페이 홈페이지에서 직접 신청하실 수 있습니다.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('확인'),
          ),
        ],
      ),
    );
  }

  /// 영수증 URL 열기
  void _openReceiptUrl(String url) {
    // URL 열기 로직 (url_launcher 패키지 사용)
    LoggerUtils.logInfo('영수증 URL 열기: $url', tag: _tag);
    // 실제 구현 시 url_launcher 패키지 사용
    ToastUtils.showInfo(context, '영수증 URL: $url');
  }

  /// 영수증 URL 복사
  Future<void> _copyReceiptUrl(String url) async {
    try {
      await Clipboard.setData(ClipboardData(text: url));
      if (mounted) {
        ToastUtils.showSuccess(context, '영수증 링크가 복사되었습니다');
      }
    } catch (e) {
      LoggerUtils.logError('영수증 URL 복사 실패', tag: _tag, error: e);
      if (mounted) {
        ToastUtils.showError(context, '링크 복사에 실패했습니다');
      }
    }
  }

  /// 실시간 동기화 안내 다이얼로그
  void _showRealtimeSyncGuide() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.sync, color: Colors.green),
            SizedBox(width: 8),
            Text('🎉 프로 플랜 활성화!'),
          ],
        ),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '프로 플랜으로 업그레이드되어 실시간 동기화 기능을 사용할 수 있습니다!',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
            ),
            SizedBox(height: 16),
            Text('📱 실시간 동기화 기능:'),
            SizedBox(height: 8),
            Text('• 여러 기기에서 데이터 실시간 동기화'),
            Text('• 클라우드 백업 및 복원'),
            Text('• 데이터 손실 방지'),
            SizedBox(height: 16),
            Text(
              '💡 My 페이지 > 실시간 동기화에서 언제든지 활성화할 수 있습니다.',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey,
                fontStyle: FontStyle.italic,
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('나중에'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              // My 페이지로 이동
              Navigator.of(context).pushReplacementNamed('/my');
            },
            child: const Text('설정하러 가기'),
          ),
        ],
      ),
    );
  }

  /// 구독 취소 확인 다이얼로그
  Future<bool> _showCancelSubscriptionDialog() async {
    return await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.warning, color: Colors.orange),
            SizedBox(width: 8),
            Text('구독 취소'),
          ],
        ),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '구독을 취소하시겠습니까?',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
            ),
            SizedBox(height: 16),
            Text('📅 구독 취소 안내:'),
            SizedBox(height: 8),
            Text('• 즉시 취소되지 않습니다'),
            Text('• 다음 결제일까지 현재 플랜을 계속 사용할 수 있습니다'),
            Text('• 다음 결제일 이후 무료 플랜으로 자동 전환됩니다'),
            Text('• 정기 결제만 중단되며 기존 결제는 환불되지 않습니다'),
            SizedBox(height: 16),
            Text(
              '💡 환불 요청은 My 페이지 > 자주 묻는 질문을 참고해주세요.',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey,
                fontStyle: FontStyle.italic,
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('취소'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('구독 취소'),
          ),
        ],
      ),
    ) ?? false;
  }

  /// 프로레이션 업그레이드 확인 다이얼로그
  Future<bool> _showProrationConfirmDialog(String planName, ProrationResult prorationResult) async {
    return await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.upgrade, color: Colors.green),
            SizedBox(width: 8),
            Text('플랜 업그레이드'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '$planName으로 업그레이드하시겠습니까?',
              style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.green.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.green.shade200),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    '💰 차액 결제 안내',
                    style: TextStyle(fontWeight: FontWeight.bold, color: Colors.green),
                  ),
                  const SizedBox(height: 8),
                  Text('현재 플랜 남은 기간: ${prorationResult.remainingDays}일'),
                  Text('현재 플랜 환불 금액: ${prorationResult.currentPlanRefund}원'),
                  Text('프로 플랜 일할 금액: ${prorationResult.newPlanProrationAmount}원'),
                  const Divider(),
                  Text(
                    '결제할 차액: ${prorationResult.chargeAmount}원',
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                      color: Colors.green,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),
            Text(
              '📅 다음 정기결제일: ${_formatDate(prorationResult.nextPaymentDate)}',
              style: const TextStyle(fontSize: 14, color: Colors.grey),
            ),
            const SizedBox(height: 8),
            const Text(
              '💡 다음 정기결제부터는 프로 플랜 전액(4,900원)이 결제됩니다.',
              style: TextStyle(fontSize: 12, color: Colors.grey),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('취소'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green,
              foregroundColor: Colors.white,
            ),
            child: Text('${prorationResult.chargeAmount}원 결제하고 업그레이드'),
          ),
        ],
      ),
    ) ?? false;
  }
}
