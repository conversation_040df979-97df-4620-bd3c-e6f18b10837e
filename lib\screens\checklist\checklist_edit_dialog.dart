import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../providers/checklist_provider.dart';
import '../../models/checklist_template.dart';
import '../../utils/app_colors.dart';
import '../../utils/dimens.dart';
import '../../utils/logger_utils.dart';
import '../../utils/toast_utils.dart';
import '../../utils/dialog_theme.dart' as custom_dialog;
import '../../widgets/unsaved_changes_dialog.dart';

/// 템플릿 변경사항을 담는 클래스
class _TemplateChanges {
  final List<ChecklistTemplate> toAdd;
  final List<ChecklistTemplate> toDelete;
  final List<ChecklistTemplate> toUpdate;

  _TemplateChanges({
    required this.toAdd,
    required this.toDelete,
    required this.toUpdate,
  });

  int get totalChanges => toAdd.length + toDelete.length + toUpdate.length;
}


/// 체크리스트 편집 다이얼로그 (새 버전)
///
/// 개선된 UX:
/// - 항상 보이는 텍스트 필드
/// - 드래그 앤 드롭으로 순서 변경
/// - 뒤로가기 버튼과 저장 버튼
/// - 일괄 저장 방식
class ChecklistEditDialog extends ConsumerStatefulWidget {
  const ChecklistEditDialog({super.key});

  @override
  ConsumerState<ChecklistEditDialog> createState() => _ChecklistEditDialogState();
}

class _ChecklistEditDialogState extends ConsumerState<ChecklistEditDialog> {
  static const String _tag = 'ChecklistEditDialog';

  final _titleController = TextEditingController();
  final _formKey = GlobalKey<FormState>();

  List<ChecklistTemplate> _templates = [];
  bool _hasChanges = false;
  bool _isSaving = false;
  ChecklistTemplate? _editingTemplate;
  final _editController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _loadTemplates();
  }

  @override
  void dispose() {
    _titleController.dispose();
    _editController.dispose();
    super.dispose();
  }

  /// 템플릿 목록 로드
  void _loadTemplates() {
    final checklistState = ref.read(checklistNotifierProvider);
    _templates = List.from(checklistState.templates);
  }

  /// 새 템플릿 추가
  void _addTemplate() {
    if (_titleController.text.trim().isEmpty) return;

    final newTemplate = ChecklistTemplate.create(
      title: _titleController.text.trim(),
      order: _templates.length,
    );

    setState(() {
      _templates.add(newTemplate);
      _hasChanges = true;
      _titleController.clear();
    });
  }

  /// 템플릿 삭제
  void _deleteTemplate(int index) {
    setState(() {
      _templates.removeAt(index);
      _hasChanges = true;
      // 순서 재정렬
      for (int i = 0; i < _templates.length; i++) {
        _templates[i] = _templates[i].copyWith(order: i);
      }
    });
  }

  /// 템플릿 수정 시작
  void _startEditTemplate(int index) {
    setState(() {
      _editingTemplate = _templates[index];
      _editController.text = _editingTemplate!.title;
    });
  }

  /// 템플릿 수정 완료
  void _finishEditTemplate() {
    if (_editingTemplate != null && _editController.text.trim().isNotEmpty) {
      final index = _templates.indexWhere((t) => t.title == _editingTemplate!.title);
      if (index != -1) {
        setState(() {
          _templates[index] = _editingTemplate!.copyWith(
            title: _editController.text.trim(),
          );
          _hasChanges = true;
          _editingTemplate = null;
          _editController.clear();
        });
      }
    }
  }

  /// 템플릿 수정 취소
  void _cancelEditTemplate() {
    setState(() {
      _editingTemplate = null;
      _editController.clear();
    });
  }

  /// 템플릿 순서 변경
  void _reorderTemplates(int oldIndex, int newIndex) {
    setState(() {
      if (newIndex > oldIndex) {
        newIndex -= 1;
      }
      final item = _templates.removeAt(oldIndex);
      _templates.insert(newIndex, item);
      _hasChanges = true;

      // 순서 재정렬
      for (int i = 0; i < _templates.length; i++) {
        _templates[i] = _templates[i].copyWith(order: i);
      }
    });
  }

  /// 뒤로가기 처리: 미저장 변경 확인
  Future<void> _onBackPressed() async {
    if (!_hasChanges) {
      if (mounted) Navigator.of(context).pop();
      return;
    }
    final confirmed = await UnsavedChangesDialog.show(
      context: context,
    );
    if (confirmed == true && mounted) {
      Navigator.of(context).pop();
    }
  }

  /// 변경사항 저장 (효율적인 방식)
  Future<void> _saveChanges() async {
    if (!_hasChanges) {
      Navigator.of(context).pop();
      return;
    }

    setState(() {
      _isSaving = true;
    });

    try {
      final notifier = ref.read(checklistNotifierProvider.notifier);
      final currentTemplates = ref.read(checklistNotifierProvider).templates;

      // 효율적인 변경사항 분석
      final changes = _analyzeChanges(currentTemplates, _templates);

      LoggerUtils.logInfo('변경사항 분석 완료 - 추가: ${changes.toAdd.length}, 삭제: ${changes.toDelete.length}, 수정: ${changes.toUpdate.length}', tag: _tag);

      // 1. 삭제할 템플릿들 (병렬 처리)
      if (changes.toDelete.isNotEmpty) {
        await Future.wait(
          changes.toDelete.map((template) => notifier.deleteTemplate(template.id!))
        );
      }

      // 2. 새로 추가할 템플릿들 (병렬 처리)
      if (changes.toAdd.isNotEmpty) {
        await Future.wait(
          changes.toAdd.map((template) => notifier.addTemplate(template))
        );
      }

      // 3. 수정할 템플릿들 (병렬 처리)
      if (changes.toUpdate.isNotEmpty) {
        await Future.wait(
          changes.toUpdate.map((template) => notifier.updateTemplate(template))
        );
      }

      if (mounted) {
        Navigator.of(context).pop();
        ToastUtils.showSuccess(context, '체크리스트가 저장되었습니다. (${changes.totalChanges}개 변경)');
      }
    } catch (e) {
      LoggerUtils.logError('체크리스트 저장 실패', tag: _tag, error: e);
      if (mounted) {
        ToastUtils.showError(context, '저장 중 오류가 발생했습니다: $e');
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSaving = false;
        });
      }
    }
  }

  /// 변경사항 분석 (효율적인 diff 알고리즘)
  _TemplateChanges _analyzeChanges(List<ChecklistTemplate> current, List<ChecklistTemplate> updated) {
    final toAdd = <ChecklistTemplate>[];
    final toDelete = <ChecklistTemplate>[];
    final toUpdate = <ChecklistTemplate>[];

    // 현재 템플릿들을 title로 매핑 (빠른 검색을 위해)
    final currentMap = <String, ChecklistTemplate>{};
    for (final template in current) {
      currentMap[template.title] = template;
    }

    // 업데이트된 템플릿들을 title로 매핑
    final updatedMap = <String, ChecklistTemplate>{};
    for (int i = 0; i < updated.length; i++) {
      final template = updated[i];
      updatedMap[template.title] = template.copyWith(order: i);
    }

    // 1. 새로 추가되거나 수정된 항목 찾기
    for (final entry in updatedMap.entries) {
      final title = entry.key;
      final newTemplate = entry.value;

      if (currentMap.containsKey(title)) {
        // 기존 항목 - 변경사항 확인
        final currentTemplate = currentMap[title]!;
        if (currentTemplate.order != newTemplate.order ||
            currentTemplate.title != newTemplate.title) {
          // 순서나 제목이 변경됨
          toUpdate.add(currentTemplate.copyWith(
            title: newTemplate.title,
            order: newTemplate.order,
            updatedAt: DateTime.now(),
          ));
        }
      } else {
        // 새 항목
        toAdd.add(ChecklistTemplate.create(
          title: newTemplate.title,
          order: newTemplate.order,
        ));
      }
    }

    // 2. 삭제된 항목 찾기
    for (final template in current) {
      if (!updatedMap.containsKey(template.title)) {
        toDelete.add(template);
      }
    }

    return _TemplateChanges(
      toAdd: toAdd,
      toDelete: toDelete,
      toUpdate: toUpdate,
    );
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (didPop, result) async {
        if (!didPop) {
          await _onBackPressed();
        }
      },
      child: Dialog(
        backgroundColor: AppColors.surface,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        insetPadding: EdgeInsets.all(custom_dialog.DialogTheme.isLandscape(context) ? 8 : 16),
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,
          constraints: BoxConstraints(
            maxHeight: custom_dialog.DialogTheme.getResponsiveMaxHeight(context),
          ),
          padding: const EdgeInsets.all(Dimens.space20),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 헤더
              Row(
                children: [
                  IconButton(
                    icon: const Icon(Icons.arrow_back, color: AppColors.onSurfaceVariant),
                    onPressed: _onBackPressed,
                    tooltip: '뒤로가기',
                  ),
                  const SizedBox(width: Dimens.space8),
                  Text(
                    '체크리스트 편집',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.w600,
                      color: AppColors.onSurface,
                    ),
                  ),
                  const Spacer(),
                  if (_isSaving)
                    const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(AppColors.primarySeed),
                      ),
                    )
                  else
                    IconButton(
                      icon: const Icon(Icons.check, color: AppColors.primarySeed),
                      onPressed: _saveChanges,
                      tooltip: '저장',
                    ),
                ],
              ),
              const SizedBox(height: Dimens.space16),

              // 새 항목 추가 폼
              Form(
                key: _formKey,
                child: Row(
                  children: [
                    Expanded(
                      child: TextFormField(
                        controller: _titleController,
                        decoration: InputDecoration(
                          hintText: '새 체크리스트 항목을 입력하세요',
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                            borderSide: BorderSide(color: AppColors.onSurfaceVariant),
                          ),
                          focusedBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                            borderSide: BorderSide(color: AppColors.primarySeed, width: 2),
                          ),
                          contentPadding: const EdgeInsets.symmetric(
                            horizontal: Dimens.space12,
                            vertical: Dimens.space12,
                          ),
                        ),
                        onFieldSubmitted: (_) => _addTemplate(),
                        maxLength: 50,
                        buildCounter: (context, {required currentLength, required isFocused, maxLength}) => null,
                      ),
                    ),
                    const SizedBox(width: Dimens.space8),
                    ElevatedButton(
                      onPressed: _addTemplate,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.primarySeed,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(
                          horizontal: Dimens.space16,
                          vertical: Dimens.space12,
                        ),
                      ),
                      child: const Text('추가'),
                    ),
                  ],
                ),
              ),

              const SizedBox(height: Dimens.space16),

              // 구분선
              Container(
                height: 1,
                color: AppColors.onSurfaceVariant.withValues(alpha: 0.3),
              ),
              const SizedBox(height: Dimens.space8),

              // 진행률 표시 (중앙 정렬)
              Center(
                child: Text(
                  '${_templates.length}/50개 항목',
                  style: TextStyle(
                    fontSize: 12,
                    color: AppColors.onSurfaceVariant,
                    fontWeight: FontWeight.w400,
                  ),
                ),
              ),
              const SizedBox(height: Dimens.space8),

              // 템플릿 목록 (스크롤 가능)
              Expanded(
                child: SingleChildScrollView(
                  child: _templates.isEmpty
                      ? _buildEmptyState()
                      : _buildTemplateList(),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 빈 상태 위젯
  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.checklist,
            size: 64,
            color: AppColors.onSurfaceVariant.withValues(alpha: 0.5),
          ),
          const SizedBox(height: Dimens.space16),
          Text(
            '체크리스트가 없습니다',
            style: TextStyle(
              fontSize: 16,
              color: AppColors.onSurfaceVariant,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: Dimens.space8),
          Text(
            '위의 입력 필드에서 새 항목을 추가해보세요',
            style: TextStyle(
              fontSize: 14,
              color: AppColors.onSurfaceVariant.withValues(alpha: 0.7),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// 템플릿 목록 위젯
  Widget _buildTemplateList() {
    return ReorderableListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: _templates.length,
      onReorder: _reorderTemplates,
      buildDefaultDragHandles: false, // 기본 드래그 핸들 비활성화
      proxyDecorator: (child, index, animation) {
        return AnimatedBuilder(
          animation: animation,
          builder: (BuildContext context, Widget? child) {
            final double animValue = Curves.easeInOut.transform(animation.value);
            final double elevation = lerpDouble(0, 6, animValue)!;
            final double scale = lerpDouble(1, 1.02, animValue)!;
            return Transform.scale(
              scale: scale,
              child: Material(
                elevation: elevation,
                borderRadius: BorderRadius.circular(8),
                child: child,
              ),
            );
          },
          child: child,
        );
      },
      itemBuilder: (context, index) {
        final template = _templates[index];
        return _buildTemplateItem(template, index);
      },
    );
  }

  /// 템플릿 아이템 위젯
  Widget _buildTemplateItem(ChecklistTemplate template, int index) {
    final isEditing = _editingTemplate?.title == template.title;

    return Container(
      key: ValueKey(template.title + index.toString()),
      margin: const EdgeInsets.only(bottom: Dimens.space6),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: isEditing
              ? AppColors.primarySeed
              : AppColors.onSurfaceVariant.withValues(alpha: 0.3),
          width: isEditing ? 2 : 1,
        ),
      ),
      child: isEditing ? _buildEditingItem(index) : _buildNormalItem(template, index),
    );
  }

  /// 일반 상태 아이템
  Widget _buildNormalItem(ChecklistTemplate template, int index) {
    return Padding(
      padding: const EdgeInsets.symmetric(
        horizontal: Dimens.space12,
        vertical: Dimens.space4,
      ),
      child: Row(
        children: [
          Expanded(
            child: Text(
              template.title,
              style: TextStyle(
                fontSize: 15,
                fontWeight: FontWeight.w500,
                color: AppColors.onSurface,
              ),
            ),
          ),
          // 수정 버튼
          IconButton(
            icon: const Icon(Icons.edit, size: 18),
            color: AppColors.onSurfaceVariant,
            onPressed: () => _startEditTemplate(index),
            tooltip: '수정',
            constraints: const BoxConstraints(minWidth: 32, minHeight: 32),
            padding: const EdgeInsets.all(4),
          ),
          // 삭제 버튼
          IconButton(
            icon: const Icon(Icons.delete, size: 18),
            color: AppColors.textColorError,
            onPressed: () => _deleteTemplate(index),
            tooltip: '삭제',
            constraints: const BoxConstraints(minWidth: 32, minHeight: 32),
            padding: const EdgeInsets.all(4),
          ),
          // 드래그 핸들
          ReorderableDragStartListener(
            index: index,
            child: Container(
              padding: const EdgeInsets.all(4),
              child: const Icon(
                Icons.drag_handle,
                color: AppColors.onSurfaceVariant,
                size: 18,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 편집 상태 아이템
  Widget _buildEditingItem(int index) {
    return Padding(
      padding: const EdgeInsets.symmetric(
        horizontal: Dimens.space12,
        vertical: Dimens.space6,
      ),
      child: Row(
        children: [
          Expanded(
            child: TextField(
              controller: _editController,
              style: TextStyle(
                fontSize: 15,
                fontWeight: FontWeight.w500,
                color: AppColors.onSurface,
              ),
              decoration: const InputDecoration(
                border: InputBorder.none,
                contentPadding: EdgeInsets.zero,
              ),
              autofocus: true,
              onSubmitted: (_) => _finishEditTemplate(),
            ),
          ),
          // 완료 버튼
          IconButton(
            icon: const Icon(Icons.check, size: 18),
            color: AppColors.primarySeed,
            onPressed: _finishEditTemplate,
            tooltip: '완료',
            constraints: const BoxConstraints(minWidth: 32, minHeight: 32),
            padding: const EdgeInsets.all(4),
          ),
          // 취소 버튼
          IconButton(
            icon: const Icon(Icons.close, size: 18),
            color: AppColors.onSurfaceVariant,
            onPressed: _cancelEditTemplate,
            tooltip: '취소',
            constraints: const BoxConstraints(minWidth: 32, minHeight: 32),
            padding: const EdgeInsets.all(4),
          ),
        ],
      ),
    );
  }
}
