<!DOCTYPE html>
<html lang="ko">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>시스템 로그 - 바라부스매니저 관리자</title>
    <link rel="icon" type="image/png" href="favicon.png">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Pretendard', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #F8F9FA;
            color: #495057;
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 24px;
        }

        /* Header Section */
        .header-section {
            background: white;
            border-bottom: 1px solid #E9ECEF;
            padding: 16px 0;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.04);
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header-title {
            font-size: 20px;
            font-weight: 600;
            color: #495057;
        }

        .header-actions {
            display: flex;
            gap: 12px;
            align-items: center;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 6px;
            transition: all 0.2s;
        }

        .btn-primary {
            background: #495057;
            color: white;
        }

        .btn-primary:hover {
            background: #343A40;
        }

        .btn-secondary {
            background: #F8F9FA;
            color: #495057;
            border: 1px solid #DEE2E6;
        }

        .btn-secondary:hover {
            background: #E9ECEF;
        }

        /* Main Content */
        .main-content {
            padding: 24px 0;
        }

        /* Tabs */
        .tabs-container {
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
            border: 1px solid #E9ECEF;
            margin-bottom: 24px;
        }

        .tabs-header {
            display: flex;
            border-bottom: 1px solid #E9ECEF;
        }

        .tab-button {
            flex: 1;
            padding: 16px 24px;
            background: none;
            border: none;
            font-size: 14px;
            font-weight: 500;
            color: #6C757D;
            cursor: pointer;
            transition: all 0.2s;
            border-bottom: 3px solid transparent;
        }

        .tab-button.active {
            color: #495057;
            border-bottom-color: #495057;
            background: #F8F9FA;
        }

        .tab-button:hover:not(.active) {
            background: #F8F9FA;
        }

        .tab-content {
            padding: 24px;
        }

        .tab-panel {
            display: none;
        }

        .tab-panel.active {
            display: block;
        }

        /* Logs Table */
        .logs-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 14px;
        }

        .logs-table th,
        .logs-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #E9ECEF;
        }

        .logs-table th {
            background: #F8F9FA;
            font-weight: 600;
            color: #495057;
        }

        .log-level {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }

        .log-level-success {
            background: #D4EDDA;
            color: #155724;
        }

        .log-level-info {
            background: #D1ECF1;
            color: #0C5460;
        }

        .log-level-warning {
            background: #FFF3CD;
            color: #856404;
        }

        .log-level-error {
            background: #F8D7DA;
            color: #721C24;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #6C757D;
        }

        .no-data {
            text-align: center;
            padding: 40px;
            color: #6C757D;
        }

        /* Pagination */
        .pagination-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 20px;
            padding: 16px;
            background: white;
            border-radius: 8px;
            border: 1px solid #E9ECEF;
        }

        .pagination-info {
            font-size: 14px;
            color: #6C757D;
        }

        .pagination-controls {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .page-size-selector {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
            color: #495057;
        }

        .page-size-selector select {
            padding: 4px 8px;
            border: 1px solid #CED4DA;
            border-radius: 4px;
            font-size: 14px;
        }

        .pagination-buttons {
            display: flex;
            gap: 4px;
        }

        .pagination-btn {
            padding: 6px 12px;
            border: 1px solid #CED4DA;
            background: white;
            color: #495057;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.2s;
        }

        .pagination-btn:hover:not(:disabled) {
            background: #E9ECEF;
        }

        .pagination-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .pagination-btn.active {
            background: #007BFF;
            color: white;
            border-color: #007BFF;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 16px;
            }
            
            .header-actions {
                width: 100%;
                justify-content: center;
            }
            
            .tabs-header {
                flex-direction: column;
            }
            
            .tab-button {
                border-bottom: 1px solid #E9ECEF;
                border-right: none;
            }
            
            .tab-button.active {
                border-bottom-color: #E9ECEF;
                border-left: 3px solid #495057;
            }
        }
    </style>
</head>
<body>
    <!-- Header Section -->
    <section class="header-section">
        <div class="container">
            <div class="header-content">
                <h1 class="header-title">시스템 로그</h1>
                <div class="header-actions">
                    <button class="btn btn-secondary" onclick="refreshLogs()">
                        🔄 새로고침
                    </button>
                    <a href="/admin-dashboard-web.html" class="btn btn-secondary">
                        ← 대시보드로
                    </a>
                    <button class="btn btn-primary" onclick="logout()">
                        🚪 로그아웃
                    </button>
                </div>
            </div>
        </div>
    </section>

    <!-- Main Content -->
    <section class="main-content">
        <div class="container">
            <div class="tabs-container">
                <div class="tabs-header">
                    <button class="tab-button active" onclick="switchTab('admin')">관리자 로그</button>
                    <button class="tab-button" onclick="switchTab('payment')">자동결제 로그</button>
                    <button class="tab-button" onclick="switchTab('access')">접속 로그</button>
                </div>

                <div class="tab-content">
                    <!-- 관리자 로그 -->
                    <div id="adminTab" class="tab-panel active">
                        <div id="adminLogsContent">
                            <div class="loading">관리자 로그를 불러오는 중...</div>
                        </div>
                    </div>

                    <!-- 자동결제 로그 -->
                    <div id="paymentTab" class="tab-panel">
                        <div id="paymentLogsContent">
                            <div class="loading">자동결제 로그를 불러오는 중...</div>
                        </div>
                    </div>

                    <!-- 접속 로그 -->
                    <div id="accessTab" class="tab-panel">
                        <div id="accessLogsContent">
                            <div class="loading">접속 로그를 불러오는 중...</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <script>
        let authToken = null;
        let currentTab = 'admin';
        let isLoading = false;

        // API 엔드포인트
        const API_ENDPOINTS = {
            getAdminSystemInfo: 'https://us-central1-parabara-1a504.cloudfunctions.net/getAdminSystemInfo'
        };

        // 페이지네이션 상태
        let currentPage = {
            admin: 1,
            payment: 1,
            access: 1
        };

        let pageSize = {
            admin: 20,
            payment: 20,
            access: 20
        };

        let totalLogs = {
            admin: 0,
            payment: 0,
            access: 0
        };

        let allLogs = {
            admin: [],
            payment: [],
            access: []
        };

        // 인증 확인
        function checkAuth() {
            authToken = localStorage.getItem('admin_token');
            if (!authToken) {
                window.location.href = '/admin-login.html';
                return false;
            }
            return true;
        }

        // 탭 전환
        function switchTab(tabName) {
            // 모든 탭 버튼과 패널 비활성화
            document.querySelectorAll('.tab-button').forEach(btn => btn.classList.remove('active'));
            document.querySelectorAll('.tab-panel').forEach(panel => panel.classList.remove('active'));

            // 선택된 탭 활성화
            document.querySelector(`[onclick="switchTab('${tabName}')"]`).classList.add('active');
            document.getElementById(`${tabName}Tab`).classList.add('active');

            currentTab = tabName;
            loadCurrentTabData();
        }

        // 현재 탭 데이터 로드
        async function loadCurrentTabData() {
            switch (currentTab) {
                case 'admin':
                    await loadAdminLogs();
                    break;
                case 'payment':
                    await loadPaymentLogs();
                    break;
                case 'access':
                    await loadAccessLogs();
                    break;
            }
        }

        // 관리자 로그 로드
        async function loadAdminLogs() {
            const container = document.getElementById('adminLogsContent');
            container.innerHTML = '<div class="loading">관리자 로그를 불러오는 중...</div>';

            try {
                const response = await fetch(API_ENDPOINTS.getAdminSystemInfo);
                const data = await response.json();

                if (data.success && data.data && data.data.adminLogs) {
                    allLogs.admin = data.data.adminLogs;
                    totalLogs.admin = allLogs.admin.length;
                    displayLogsWithPagination(container, 'admin');
                } else {
                    container.innerHTML = '<div class="no-data">관리자 로그가 없습니다.</div>';
                }
            } catch (error) {
                console.error('관리자 로그 로드 실패:', error);
                container.innerHTML = '<div class="no-data">로그를 불러오는데 실패했습니다.</div>';
            }
        }

        // 자동결제 로그 로드
        async function loadPaymentLogs() {
            const container = document.getElementById('paymentLogsContent');
            container.innerHTML = '<div class="loading">자동결제 로그를 불러오는 중...</div>';

            try {
                const response = await fetch(API_ENDPOINTS.getAdminSystemInfo);
                const data = await response.json();

                if (data.success && data.data && data.data.autoPaymentLogs) {
                    allLogs.payment = data.data.autoPaymentLogs;
                    totalLogs.payment = allLogs.payment.length;
                    displayLogsWithPagination(container, 'payment');
                } else {
                    container.innerHTML = '<div class="no-data">자동결제 로그가 없습니다.</div>';
                }
            } catch (error) {
                console.error('자동결제 로그 로드 실패:', error);
                container.innerHTML = '<div class="no-data">로그를 불러오는데 실패했습니다.</div>';
            }
        }

        // 접속 로그 로드
        async function loadAccessLogs() {
            const container = document.getElementById('accessLogsContent');
            container.innerHTML = '<div class="loading">접속 로그를 불러오는 중...</div>';

            try {
                const response = await fetch(API_ENDPOINTS.getAdminSystemInfo);
                const data = await response.json();

                if (data.success && data.data && data.data.accessLogs) {
                    allLogs.access = data.data.accessLogs;
                    totalLogs.access = allLogs.access.length;
                    displayLogsWithPagination(container, 'access');
                } else {
                    container.innerHTML = '<div class="no-data">접속 로그가 없습니다.</div>';
                }
            } catch (error) {
                console.error('접속 로그 로드 실패:', error);
                container.innerHTML = '<div class="no-data">로그를 불러오는데 실패했습니다.</div>';
            }
        }

        // 페이지네이션과 함께 로그 표시
        function displayLogsWithPagination(container, type) {
            const logs = allLogs[type];
            const total = totalLogs[type];
            const current = currentPage[type];
            const size = pageSize[type];

            if (!logs || logs.length === 0) {
                container.innerHTML = '<div class="no-data">로그가 없습니다.</div>';
                return;
            }

            // 현재 페이지의 로그만 추출
            const startIndex = (current - 1) * size;
            const endIndex = startIndex + size;
            const currentLogs = logs.slice(startIndex, endIndex);

            const totalPages = Math.ceil(total / size);

            const tableHtml = `
                <table class="logs-table">
                    <thead>
                        <tr>
                            <th>시간</th>
                            <th>레벨</th>
                            <th>메시지</th>
                            <th>상세정보</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${currentLogs.map(log => `
                            <tr>
                                <td>${formatDate(log.timestamp)}</td>
                                <td>
                                    <span class="log-level log-level-${getLogLevelClass(log)}">
                                        ${getLogLevel(log, type)}
                                    </span>
                                </td>
                                <td>${getLogMessage(log, type)}</td>
                                <td>${getLogDetails(log, type)}</td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>

                <div class="pagination-container">
                    <div class="pagination-info">
                        총 ${total}개 중 ${startIndex + 1}-${Math.min(endIndex, total)}개 표시
                    </div>
                    <div class="pagination-controls">
                        <div class="page-size-selector">
                            <span>페이지당</span>
                            <select onchange="changePageSize('${type}', this.value)">
                                <option value="20" ${size === 20 ? 'selected' : ''}>20개</option>
                                <option value="50" ${size === 50 ? 'selected' : ''}>50개</option>
                                <option value="100" ${size === 100 ? 'selected' : ''}>100개</option>
                            </select>
                        </div>
                        <div class="pagination-buttons">
                            <button class="pagination-btn" onclick="changePage('${type}', 1)" ${current === 1 ? 'disabled' : ''}>
                                첫 페이지
                            </button>
                            <button class="pagination-btn" onclick="changePage('${type}', ${current - 1})" ${current === 1 ? 'disabled' : ''}>
                                이전
                            </button>
                            ${generatePageButtons(current, totalPages, type)}
                            <button class="pagination-btn" onclick="changePage('${type}', ${current + 1})" ${current === totalPages ? 'disabled' : ''}>
                                다음
                            </button>
                            <button class="pagination-btn" onclick="changePage('${type}', ${totalPages})" ${current === totalPages ? 'disabled' : ''}>
                                마지막 페이지
                            </button>
                        </div>
                    </div>
                </div>
            `;

            container.innerHTML = tableHtml;
        }

        // 로그 표시 (기존 함수 유지)
        function displayLogs(container, logs, type) {
            if (!logs || logs.length === 0) {
                container.innerHTML = '<div class="no-data">로그가 없습니다.</div>';
                return;
            }

            const tableHtml = `
                <table class="logs-table">
                    <thead>
                        <tr>
                            <th>시간</th>
                            <th>레벨</th>
                            <th>메시지</th>
                            <th>상세정보</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${logs.map(log => `
                            <tr>
                                <td>${formatDate(log.timestamp)}</td>
                                <td>
                                    <span class="log-level log-level-${getLogLevelClass(log)}">
                                        ${getLogLevel(log, type)}
                                    </span>
                                </td>
                                <td>${getLogMessage(log, type)}</td>
                                <td>${getLogDetails(log, type)}</td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            `;

            container.innerHTML = tableHtml;
        }

        // 날짜 포맷
        function formatDate(timestamp) {
            if (!timestamp) return 'N/A';

            try {
                // 다양한 날짜 형식 처리
                let date;
                if (typeof timestamp === 'string') {
                    // ISO 문자열이나 다른 문자열 형식
                    date = new Date(timestamp);
                } else if (typeof timestamp === 'object' && timestamp._seconds) {
                    // Firestore Timestamp 객체
                    const seconds = timestamp._seconds;
                    const nanoseconds = timestamp._nanoseconds || 0;
                    date = new Date(seconds * 1000 + Math.round(nanoseconds / 1000000));
                } else if (typeof timestamp === 'number') {
                    // Unix timestamp (초 또는 밀리초)
                    date = new Date(timestamp > 1000000000000 ? timestamp : timestamp * 1000);
                } else if (typeof timestamp === 'object' && timestamp.seconds) {
                    // 다른 형태의 Firestore Timestamp
                    date = new Date(timestamp.seconds * 1000);
                } else {
                    date = new Date(timestamp);
                }

                // 유효한 날짜인지 확인
                if (isNaN(date.getTime())) {
                    console.warn('Invalid timestamp:', timestamp);
                    return 'Invalid Date';
                }

                // 한국 시간으로 표시 (UTC+9)
                const koreaTime = new Date(date.getTime() + (9 * 60 * 60 * 1000));
                return koreaTime.toLocaleString('ko-KR', {
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit',
                    hour: '2-digit',
                    minute: '2-digit',
                    second: '2-digit',
                    hour12: false
                });
            } catch (error) {
                console.error('날짜 파싱 오류:', error, timestamp);
                return 'Invalid Date';
            }
        }

        // 로그 레벨 클래스
        function getLogLevelClass(log) {
            if (log.success === true) return 'success';
            if (log.success === false) return 'error';
            return 'info';
        }

        // 로그 레벨 텍스트
        function getLogLevel(log, type) {
            if (log.success === true) return 'SUCCESS';
            if (log.success === false) return 'ERROR';
            return 'INFO';
        }

        // 로그 메시지
        function getLogMessage(log, type) {
            switch (type) {
                case 'admin':
                    return '관리자 로그인';
                case 'payment':
                    return '자동결제 처리';
                case 'access':
                    return '사용자 접속';
                default:
                    return log.message || 'N/A';
            }
        }

        // 로그 상세정보
        function getLogDetails(log, type) {
            switch (type) {
                case 'admin':
                    return `${log.username || 'Unknown'} - ${log.ip || 'Unknown IP'}`;
                case 'payment':
                    return `${log.userId || 'Unknown'} - ${log.amount || 'N/A'}원`;
                case 'access':
                    return `${log.userId || 'Unknown'} - ${log.ip || 'Unknown IP'}`;
                default:
                    return log.details || 'N/A';
            }
        }

        // 모든 로그 새로고침
        async function refreshLogs() {
            if (isLoading) return;
            
            isLoading = true;
            const refreshBtn = document.querySelector('[onclick="refreshLogs()"]');
            refreshBtn.disabled = true;
            refreshBtn.textContent = '🔄 로딩중...';
            
            try {
                await loadCurrentTabData();
            } finally {
                isLoading = false;
                refreshBtn.disabled = false;
                refreshBtn.textContent = '🔄 새로고침';
            }
        }

        // 로그아웃
        function logout() {
            if (confirm('로그아웃하시겠습니까?')) {
                localStorage.removeItem('admin_token');
                window.location.href = '/admin-login.html';
            }
        }

        // 페이지 버튼 생성
        function generatePageButtons(current, total, type) {
            let buttons = '';
            const maxButtons = 5;
            let start = Math.max(1, current - Math.floor(maxButtons / 2));
            let end = Math.min(total, start + maxButtons - 1);

            if (end - start + 1 < maxButtons) {
                start = Math.max(1, end - maxButtons + 1);
            }

            for (let i = start; i <= end; i++) {
                buttons += `
                    <button class="pagination-btn ${i === current ? 'active' : ''}"
                            onclick="changePage('${type}', ${i})">
                        ${i}
                    </button>
                `;
            }

            return buttons;
        }

        // 페이지 변경
        function changePage(type, page) {
            currentPage[type] = page;
            const container = document.getElementById(type === 'admin' ? 'adminLogsContent' :
                                                   type === 'payment' ? 'paymentLogsContent' : 'accessLogsContent');
            displayLogsWithPagination(container, type);
        }

        // 페이지 크기 변경
        function changePageSize(type, size) {
            pageSize[type] = parseInt(size);
            currentPage[type] = 1; // 첫 페이지로 리셋
            const container = document.getElementById(type === 'admin' ? 'adminLogsContent' :
                                                   type === 'payment' ? 'paymentLogsContent' : 'accessLogsContent');
            displayLogsWithPagination(container, type);
        }

        // 페이지 로드 시 초기화
        document.addEventListener('DOMContentLoaded', async function() {
            if (!checkAuth()) return;
            await loadCurrentTabData();
        });
    </script>
</body>
</html>
